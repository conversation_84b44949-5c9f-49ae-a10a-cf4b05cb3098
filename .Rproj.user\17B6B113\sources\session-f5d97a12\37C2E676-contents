# Ultra Conservative Parameter Changes - QSP Thyroid Model

## Problem Statement

The previous recalibration still produced unrealistically high hypothyroidism incidence rates:
- **Observed**: 58% any hypothyroidism, 52% grade 2+
- **Target**: 10-20% any hypothyroidism, 5-15% grade 2+
- **Issue**: Parameters were not conservative enough

## Ultra Conservative Parameter Adjustments

### 1. Core Thyrocyte Dynamics (Most Critical)

| Parameter | Original | Previous | Ultra Conservative | Change |
|-----------|----------|----------|-------------------|---------|
| `k_death` | 0.008 | 0.003 | **0.001** | -87.5% from original |
| `EC50_IFN_death` | 100 | 200 | **500** | +400% from original |

**Rationale**: These parameters directly control thyrocyte death rate and damage threshold. Ultra-conservative values make thyrocytes much more resistant to cytokine-induced damage.

### 2. Cytokine Dynamics (Critical)

| Parameter | Original | Previous | Ultra Conservative | Change |
|-----------|----------|----------|-------------------|---------|
| `epsilon` | 0.3 | 0.15 | **0.05** | -83% from original |
| `k_clear_IFN` | 8.0 | 12.0 | **20.0** | +150% from original |
| `cytokine_threshold_pg_ml` | 100 | 200 | **1000** | +900% from original |

**Rationale**: Dramatically reduce cytokine production, increase clearance, and raise damage threshold to minimize inflammatory response.

### 3. Drug Activation Thresholds (Very Conservative)

| Drug | Original | Previous | Ultra Conservative | Change |
|------|----------|----------|-------------------|---------|
| Nivolumab | 45,000 | 80,000 | **150,000** | +233% from original |
| Pembrolizumab | 30,000 | 90,000 | **180,000** | +500% from original |
| Atezolizumab | 200,000 | 300,000 | **500,000** | +150% from original |
| Durvalumab | 150,000 | 350,000 | **600,000** | +300% from original |

**Rationale**: Much higher drug concentrations required for immune activation, reducing the number of patients who develop immune responses.

### 4. Immune Susceptibility Rates (Halved)

| Drug | Original | Previous | Ultra Conservative | Change |
|------|----------|----------|-------------------|---------|
| Nivolumab | 8% | 15% | **8%** | Back to original (was too high) |
| Pembrolizumab | 12% | 12% | **6%** | -50% from previous |
| Atezolizumab | 5.5% | 8% | **4%** | -27% from original |
| Durvalumab | 5% | 6% | **3%** | -40% from original |

**Rationale**: Fewer patients classified as susceptible to immune-mediated thyroid dysfunction.

### 5. Covariate Effects (Extremely Conservative)

#### Low-Susceptible Patients (80% of susceptible population)

| Parameter | Original | Previous | Ultra Conservative | Change |
|-----------|----------|----------|-------------------|---------|
| `k_death` multiplier | 0.5 | 0.3 | **0.1** | -80% from original |
| `epsilon` multiplier | 0.3 | 0.2 | **0.05** | -83% from original |
| `EC50_IFN_death` multiplier | 2.0 | 3.0 | **5.0** | +150% from original |
| `T_eff0` multiplier | 0.5 | 0.3 | **0.1** | -80% from original |

#### High-Susceptible Patients (20% of susceptible population)

| Parameter | Original | Previous | Ultra Conservative | Change |
|-----------|----------|----------|-------------------|---------|
| `k_death` multiplier | 1.5 | 1.0 | **0.5** | -67% from original |
| `epsilon` multiplier | 0.8 | 0.5 | **0.2** | -75% from original |
| `EC50_IFN_death` multiplier | 0.8 | 1.2 | **2.0** | +150% from original |
| `T_eff0` multiplier | 2.0 | 1.5 | **0.8** | -60% from original |

**Rationale**: Even susceptible patients have much more conservative immune response parameters.

## Expected Impact

### Target Incidence Rates (Ultra Conservative)

| Drug | Target Any Hypothyroidism | Target Grade 2+ Hypothyroidism |
|------|---------------------------|--------------------------------|
| Nivolumab | 8-15% | 4-10% |
| Pembrolizumab | 6-12% | 3-8% |
| Atezolizumab | 4-8% | 2-5% |
| Durvalumab | 3-6% | 1-4% |

### Overall Population Targets
- **Any hypothyroidism**: 5-12% (down from 58%)
- **Grade 2+ hypothyroidism**: 3-8% (down from 52%)

## Biological Plausibility

These ultra-conservative parameters maintain biological plausibility by:

1. **Preserving drug ranking**: Nivolumab > Pembrolizumab > Atezolizumab > Durvalumab
2. **Maintaining mechanism**: Immune activation → cytokine production → thyrocyte damage
3. **Keeping population heterogeneity**: Susceptible vs non-susceptible patients
4. **Preserving covariate effects**: Age, sex, HLA, TPO-Ab still influence risk

## Testing Strategy

1. **Run ultra-conservative test**: `source("R/test_ultra_conservative.R")`
2. **Validate with small population** (n=50) first
3. **Scale to larger population** (n=200) if promising
4. **Check against target ranges** for each drug
5. **Fine-tune if needed** based on results

## Files Modified

1. **`R/qsp_model_core.R`**:
   - Updated all core parameters to ultra-conservative values
   - Modified drug activation thresholds
   - Adjusted susceptibility rates
   - Made covariate effects extremely conservative

2. **`R/example_usage.R`**:
   - Updated parameter documentation
   - Revised target expectations

3. **`R/test_ultra_conservative.R`** (NEW):
   - Comprehensive testing script
   - Validates against target ranges
   - Provides specific recommendations

## Risk Assessment

### Potential Issues:
- **Too conservative**: Rates might drop below clinical reality (< 5%)
- **Loss of drug differentiation**: All drugs might have similar low rates
- **Reduced sensitivity**: Model might not capture real clinical variation

### Mitigation:
- Test with multiple population sizes
- Monitor drug ranking preservation
- Fine-tune if rates are too low
- Validate against clinical data if available

## Success Criteria

The ultra-conservative recalibration is successful if:

✅ **Primary**: Overall any hypothyroidism 10-20%  
✅ **Primary**: Overall grade 2+ hypothyroidism 5-15%  
✅ **Secondary**: Drug ranking preserved (Nivolumab highest, Durvalumab lowest)  
✅ **Secondary**: Individual drug rates within target ranges  
✅ **Tertiary**: Realistic time-to-onset patterns maintained  
✅ **Tertiary**: Population heterogeneity preserved  

## Next Steps

1. **Test immediately**: Run `source("R/test_ultra_conservative.R")`
2. **Validate results**: Check if rates are now in target ranges
3. **Fine-tune if needed**: Adjust parameters based on test results
4. **Run full example**: Use `source("R/example_usage.R")` once validated
5. **Document final parameters**: Update all documentation with successful values

## Contact

For questions about these ultra-conservative parameter changes, please contact the QSP Modeling Team.

---

**Note**: These parameters represent an extremely conservative approach to achieve realistic clinical incidence rates. They may need fine-tuning based on test results, but should dramatically reduce the unrealistically high rates observed previously.
