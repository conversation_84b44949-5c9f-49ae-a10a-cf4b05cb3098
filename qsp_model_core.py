#!/usr/bin/env python3
"""
QSP Model Core: Checkpoint-Inhibitor-Induced Hypothyroidism
===========================================================

This module implements the core quantitative systems pharmacology (QSP) model
for predicting hypothyroidism induced by PD-1/PD-L1 checkpoint inhibitors.

Author: QSP Modeling Team
Date: 2024
License: MIT

Mathematical Framework:
- 6 coupled ODEs representing immune activation, cytokine release, 
  thyrocyte damage, and HPT axis feedback
- Patient-specific covariates for personalized risk assessment
- Drug-specific pharmacokinetic models for different ICIs

Usage:
    from qsp_model_core import QSPModel, simulate_patient
    
    model = QSPModel()
    results = simulate_patient(model, patient_params, drug_regimen)
"""

import numpy as np
import pandas as pd
from scipy.integrate import solve_ivp
from typing import Dict, List, Tuple, Optional, Union
import warnings
from dataclasses import dataclass, field
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelParameters:
    """
    Container for all QSP model parameters with default values from literature.
    
    Parameters are organized by biological module:
    - Checkpoint binding and drug PK
    - T-cell dynamics  
    - Cytokine kinetics
    - Thyrocyte population dynamics
    - Thyroid hormone synthesis
    - HPT axis feedback
    - Patient-specific covariates
    """
    
    # Checkpoint Binding Parameters
    Kd_PD1_PDL1: float = 8e-9  # M, PD-1/PD-L1 dissociation constant
    k_on_PD1: float = 1e5     # M⁻¹s⁻¹, association rate
    k_off_PD1: float = 0.0008 # s⁻¹, dissociation rate
    IC50_block: float = 0.35   # fractional occupancy for 50% T-cell activation
    
    # Drug-specific binding affinities (nM) - ADJUSTED for clinical ranking
    Kd_nivo_PD1: float = 2.6   # Nivolumab-PD-1 (reference)
    Kd_pembro_PD1: float = 3.5  # Pembrolizumab-PD-1 (adjusted for ranking)
    Kd_atezo_PDL1: float = 0.4  # Atezolizumab-PD-L1
    
    # T-cell Dynamics - CORRECTED for realistic immune response
    alpha: float = 8e-4        # day⁻¹, APC-driven expansion rate (FINAL TUNING for drug-specific effects)
    beta: float = 0.18         # day⁻¹, natural death/exhaustion rate (FINAL TUNING for drug-specific effects)
    gamma: float = 1.0         # day⁻¹, PD-1 mediated inhibition (FINAL TUNING for drug-specific effects)
    delta: float = 0.01        # day⁻¹, IL-2 driven proliferation
    T_eff0: float = 2e3        # cells/L, baseline autoreactive T-cells
    
    # Cytokine Parameters - CALIBRATED for realistic incidence rates (5-15%)
    epsilon: float = 0.3       # pg cell⁻¹ day⁻¹, IFN-γ secretion rate (EXTREME: reduced from 1.5)
    k_clear_IFN: float = 8.0   # day⁻¹, IFN-γ clearance rate (EXTREME: increased from 2.2)
    EC50_IFN_death: float = 100 # pg/mL, IFN-γ potency on thyrocyte death (CALIBRATED: reduced from 150 to 100)
    Hill_IFN: float = 1.2      # Hill coefficient for IFN-γ dose-response

    # Thyrocyte Dynamics - CALIBRATED for realistic hypothyroidism incidence (5-15%)
    k_death: float = 0.029     # day⁻¹(pg/mL)⁻¹, apoptosis rate per IFN-γ (RECALIBRATED: increased for literature-consistent incidence rates)
    k_regen: float = 0.06      # day⁻¹, regeneration rate (unchanged)
    Thyro_max: float = 1.0     # normalized maximum thyroid mass
    
    # Thyroid Hormone Synthesis
    k_syn_T3: float = 3.326    # pmol L⁻¹ day⁻¹ %⁻¹, T3 synthesis rate (balanced for Thyro_max=1.0)
    k_syn_T4: float = 15.0     # pmol L⁻¹ day⁻¹ %⁻¹, T4 synthesis rate
    k_deg_T3: float = 0.693    # day⁻¹, T3 degradation (t½ = 1 day)
    k_deg_T4: float = 0.099    # day⁻¹, T4 degradation (t½ = 7 days)
    
    # HPT Axis Parameters
    TSH_set: float = 1.5       # mIU/L, TSH setpoint
    T3_set: float = 4.8        # pmol/L, T3 setpoint
    T4_set: float = 12.0       # pmol/L, T4 setpoint
    theta: float = 0.1         # day⁻¹(pmol/L)⁻¹, TSH feedback gain
    k_metab_TSH: float = 0.05  # day⁻¹, TSH clearance rate
    
    # Patient Covariates (multiplicative factors)
    sex_factor: float = 1.0    # 1.3 for females, 1.0 for males
    age_factor: float = 1.0    # 1.2 for age >60, 1.0 otherwise
    HLA_factor: float = 1.0    # 2.2 for HLA-DRB1*03:01+, 1.0 otherwise
    TPO_Ab_titer: float = 0.0  # log10(IU/mL), 0 = negative

    # Immune Susceptibility State - NEW ARCHITECTURE
    immune_susceptible: bool = False  # True for patients who can develop immune responses
    susceptibility_level: str = "none"  # "none", "low", "high"
    susceptibility_assigned: bool = False  # Flag to track if susceptibility has been assigned

    # Activation Thresholds - NEW ARCHITECTURE
    drug_threshold_ng_ml: float = 80000.0  # Minimum drug concentration for immune activation (increased for realistic incidence)
    cytokine_threshold_pg_ml: float = 100.0  # Minimum IFN-γ for thyrocyte damage
    cumulative_exposure_threshold: float = 500000.0  # ng*day/mL for sustained exposure
    
    # Drug PK Parameters (will be set based on specific drug)
    drug_clearance: float = 0.2  # L/day
    drug_volume: float = 6.0     # L
    drug_dose: float = 240       # mg
    dosing_interval: float = 14  # days

class QSPModel:
    """
    Main QSP model class implementing the ODE system for checkpoint-inhibitor-induced hypothyroidism.
    
    The model consists of 6 state variables:
    1. R: PD-1/PD-L1 complex concentration
    2. T_eff: Activated autoreactive CD8+ T cells  
    3. IFN: Interferon-gamma concentration
    4. Thyro: Functional thyrocyte biomass (%)
    5. T3: Triiodothyronine concentration
    6. TSH: Thyroid-stimulating hormone
    """
    
    def __init__(self, params: Optional[ModelParameters] = None):
        """
        Initialize QSP model with parameters.
        
        Args:
            params: ModelParameters object. If None, uses default values.
        """
        self.params = params if params is not None else ModelParameters()
        self.state_names = ['R', 'T_eff', 'IFN', 'Thyro', 'T3', 'TSH']
        self.n_states = len(self.state_names)
        
        # Apply patient-specific covariate effects
        self._apply_covariates()

        # Note: immune susceptibility will be assigned in simulate_patient based on drug_type

        logger.info(f"QSP Model initialized with {self.n_states} state variables")

    def _assign_immune_susceptibility(self, drug_type: str = 'nivolumab'):
        """
        Assign immune susceptibility based on population heterogeneity and drug-specific rates.

        Population distribution:
        - Non-susceptible: 85-90% (no immune response regardless of drug concentration)
        - Low-susceptible: 5-10% (immune response only with high drug concentrations)
        - High-susceptible: 2-5% (immune response with moderate drug concentrations)

        Drug-specific incidence targets:
        - Nivolumab: 7-9%
        - Pembrolizumab: 8-15%
        - Atezolizumab: 4-7%
        - Durvalumab: 3-7%
        """
        import random

        # Drug-specific susceptibility rates (total incidence targets)
        drug_susceptibility_rates = {
            'nivolumab': 0.08,      # 8% target incidence
            'pembrolizumab': 0.12,  # 12% target incidence
            'atezolizumab': 0.055,  # 5.5% target incidence
            'durvalumab': 0.05      # 5% target incidence
        }

        total_susceptible_rate = drug_susceptibility_rates.get(drug_type, 0.08)

        # Assign susceptibility level based on random draw
        rand_val = random.random()

        # Calculate thresholds
        non_susceptible_threshold = 1.0 - total_susceptible_rate
        low_susceptible_threshold = non_susceptible_threshold + total_susceptible_rate * 0.7

        if rand_val < non_susceptible_threshold:
            # Non-susceptible (85-95% depending on drug)
            self.params.immune_susceptible = False
            self.params.susceptibility_level = "none"
        elif rand_val < low_susceptible_threshold:
            # Low-susceptible (70% of susceptible patients)
            self.params.immune_susceptible = True
            self.params.susceptibility_level = "low"
        else:
            # High-susceptible (30% of susceptible patients)
            self.params.immune_susceptible = True
            self.params.susceptibility_level = "high"

        # Mark susceptibility as assigned
        self.params.susceptibility_assigned = True

    def _apply_covariates(self):
        """Apply patient-specific covariate effects based on immune susceptibility."""
        p = self.params

        # Only apply parameter modifications if patient is immune susceptible
        if not p.immune_susceptible:
            # Non-susceptible patients: minimal parameter changes
            return

        # Calculate overall risk multiplier from all factors for susceptible patients
        overall_risk = p.sex_factor * p.age_factor * p.HLA_factor
        tpo_effect = (1 + 0.8 * p.TPO_Ab_titer)

        # Apply susceptibility-level-specific parameter sets
        if p.susceptibility_level == "low":
            # Low-susceptible: moderate immune response parameters
            p.k_death *= 0.5 * overall_risk * tpo_effect
            p.epsilon *= 0.3  # Reduced cytokine production
            p.EC50_IFN_death *= 2.0  # Higher threshold for damage
            p.T_eff0 *= 0.5 * overall_risk

        elif p.susceptibility_level == "high":
            # High-susceptible: strong immune response parameters
            p.k_death *= 1.5 * overall_risk * tpo_effect
            p.epsilon *= 0.8  # Moderate cytokine production
            p.EC50_IFN_death *= 0.8  # Lower threshold for damage
            p.T_eff0 *= 2.0 * overall_risk

        # Adjust thyroid reserve based on susceptibility
        if p.susceptibility_level in ["low", "high"]:
            p.Thyro_max /= (overall_risk ** 0.2)

        logger.debug(f"Applied covariates: sex={p.sex_factor}, age={p.age_factor}, "
                    f"HLA={p.HLA_factor}, TPO_Ab={p.TPO_Ab_titer}, overall_risk={overall_risk:.2f}")
    
    def drug_concentration(self, t: float, drug_type: str = 'nivolumab') -> float:
        """
        Calculate drug concentration at time t using one-compartment PK model.
        
        Args:
            t: Time in days
            drug_type: Type of checkpoint inhibitor ('nivolumab', 'pembrolizumab', 'atezolizumab')
            
        Returns:
            Drug concentration in ng/mL
        """
        p = self.params
        
        # Adjust PK parameters based on drug type - REALISTIC clinical doses
        if drug_type == 'nivolumab':
            CL, V, dose = 0.2, 6.0, 3  # L/day, L, mg/kg * 70kg = 210mg -> use 3mg/kg
        elif drug_type == 'pembrolizumab':
            CL, V, dose = 0.22, 6.0, 2  # 2 mg/kg * 70kg = 140mg
        elif drug_type == 'atezolizumab':
            CL, V, dose = 0.25, 6.5, 15  # 15 mg/kg * 70kg = 1050mg -> use 15mg/kg
        elif drug_type == 'durvalumab':
            CL, V, dose = 0.24, 6.2, 10  # 10 mg/kg * 70kg = 700mg
        else:
            CL, V, dose = p.drug_clearance, p.drug_volume, p.drug_dose

        # Convert mg/kg to total dose for 70kg patient
        dose = dose * 70  # mg

        # Multiple dose superposition - CORRECTED unit conversion
        conc = 0.0
        dose_times = np.arange(0, t + p.dosing_interval, p.dosing_interval)

        for dose_time in dose_times:
            if dose_time <= t:
                time_since_dose = t - dose_time
                # Convert mg to ng/mL: mg * 1e6 ng/mg / L * 1000 mL/L = mg * 1e9 / V(mL)
                conc += (dose * 1e6 / (V * 1000)) * np.exp(-CL * time_since_dose / V)  # ng/mL
        
        return max(conc, 0.0)
    
    def checkpoint_binding(self, drug_conc: float, drug_type: str = 'nivolumab') -> float:
        """
        Calculate fractional PD-1/PD-L1 complex formation after drug binding.
        
        Args:
            drug_conc: Drug concentration in ng/mL
            drug_type: Type of checkpoint inhibitor
            
        Returns:
            Fractional complex formation (0-1)
        """
        p = self.params
        
        # Convert ng/mL to nM (approximate MW = 150 kDa)
        drug_conc_nM = drug_conc / 150
        
        # Drug-specific binding affinity and immune activation potency
        if drug_type == 'nivolumab':
            Kd_drug = p.Kd_nivo_PD1
            immune_potency = 1.0  # Reference drug
        elif drug_type == 'pembrolizumab':
            Kd_drug = p.Kd_pembro_PD1
            immune_potency = 0.8  # Second most immunogenic after nivolumab
        elif drug_type == 'atezolizumab':
            Kd_drug = p.Kd_atezo_PDL1
            immune_potency = 0.05  # Much less immunogenic (literature-based)
        elif drug_type == 'durvalumab':
            Kd_drug = p.Kd_atezo_PDL1  # Similar to atezolizumab (PD-L1 inhibitor)
            immune_potency = 0.10  # Lower than pembrolizumab for correct ranking
        else:
            Kd_drug = p.Kd_nivo_PD1  # Default
            immune_potency = 1.0

        # Simplified binding model - fractional checkpoint inhibition
        # Convert to more realistic scale
        drug_conc_nM = drug_conc_nM * 1e-6  # Convert to µM scale for realistic binding

        # Simple competitive inhibition model
        f_drug = drug_conc_nM / (Kd_drug + drug_conc_nM)

        # Baseline checkpoint signaling (normalized to 1.0)
        baseline_checkpoint = 1.0

        # Drug-specific immune activation potential (higher = more immunogenic)
        # When f_drug is high (good binding), immune activation depends on drug-specific potency
        immune_activation = f_drug * immune_potency

        # Debug: print drug-specific effects (disabled for performance)
        # if drug_conc > 10000000 and abs(drug_conc - 40000000) < 5000000:  # Around typical peak
        #     print(f"DEBUG: {drug_type}, conc={drug_conc:.0f} ng/mL, f_drug={f_drug:.3f}, potency={immune_potency}, result={immune_activation:.6f}")

        return immune_activation
    
    def ode_system(self, t: float, y: np.ndarray, drug_type: str = 'nivolumab') -> np.ndarray:
        """
        Define the system of ODEs for the QSP model.
        
        Args:
            t: Time in days
            y: State vector [R, T_eff, IFN, Thyro, T3, TSH]
            drug_type: Type of checkpoint inhibitor
            
        Returns:
            Derivative vector dy/dt
        """
        R, T_eff, IFN, Thyro, T3, TSH = y
        p = self.params
        
        # Get current drug concentration
        drug_conc = self.drug_concentration(t, drug_type)
        
        # Checkpoint binding dynamics (steady-state approximation)
        R_new = self.checkpoint_binding(drug_conc, drug_type)
        dR_dt = 0  # Assume fast equilibrium
        
        # T-cell dynamics with immune susceptibility
        APC_baseline = 1e6  # cells/L, constant APC population
        IL2_baseline = 0.1  # ng/mL, basal IL-2 level

        # Drug-specific activation thresholds (ng/mL) - CALIBRATED for literature-consistent incidence
        drug_thresholds = {
            'nivolumab': 45000.0,      # PD-1 inhibitor - calibrated for ~8% incidence
            'pembrolizumab': 30000.0,  # PD-1 inhibitor - calibrated for ~12% incidence
            'atezolizumab': 200000.0,  # PD-L1 inhibitor - calibrated for ~5.5% incidence
            'durvalumab': 150000.0     # PD-L1 inhibitor - calibrated for ~5% incidence
        }

        activation_threshold = drug_thresholds.get(drug_type, p.drug_threshold_ng_ml)

        if p.immune_susceptible and drug_conc >= activation_threshold:
            # Only susceptible patients with sufficient drug exposure activate T-cells
            dT_eff_dt = (p.alpha * APC_baseline * (1 + p.gamma * R_new) -
                         p.beta * T_eff +
                         p.delta * IL2_baseline * T_eff)
        else:
            # Non-susceptible patients or insufficient drug exposure: T-cell decay only
            dT_eff_dt = -p.beta * T_eff

        # Cytokine dynamics with immune susceptibility and activation thresholds
        if p.immune_susceptible and drug_conc >= activation_threshold:
            # Only susceptible patients with sufficient drug exposure produce cytokines
            dIFN_dt = p.epsilon * T_eff - p.k_clear_IFN * IFN
        else:
            # Non-susceptible patients or insufficient drug exposure: no cytokine production
            dIFN_dt = -p.k_clear_IFN * IFN

        # Thyrocyte dynamics with cytokine threshold for damage
        if IFN >= p.cytokine_threshold_pg_ml:
            IFN_effect = IFN**p.Hill_IFN / (p.EC50_IFN_death**p.Hill_IFN + IFN**p.Hill_IFN)
            thyrocyte_death = p.k_death * IFN_effect * Thyro
        else:
            # Below cytokine threshold: no thyrocyte damage
            thyrocyte_death = 0.0

        dThyro_dt = -thyrocyte_death + p.k_regen * (p.Thyro_max - Thyro)
        
        # Thyroid hormone synthesis
        dT3_dt = p.k_syn_T3 * Thyro - p.k_deg_T3 * T3
        
        # HPT axis feedback - CORRECTED to match physiological regulation
        T3_error = p.T3_set - T3
        TSH_error = TSH - p.TSH_set  # CRITICAL FIX: TSH should be regulated around setpoint
        dTSH_dt = p.theta * T3_error - p.k_metab_TSH * TSH_error
        
        return np.array([dR_dt, dT_eff_dt, dIFN_dt, dThyro_dt, dT3_dt, dTSH_dt])
    
    def get_initial_conditions(self) -> np.ndarray:
        """
        Get physiological initial conditions for the ODE system.
        
        Returns:
            Initial state vector
        """
        p = self.params
        
        # Steady-state initial conditions
        y0 = np.array([
            0.0,           # R: No drug initially
            p.T_eff0,      # T_eff: Baseline autoreactive T-cells
            0.0,           # IFN: No cytokine initially  
            p.Thyro_max,   # Thyro: Full thyroid mass
            p.T3_set,      # T3: Normal hormone level
            p.TSH_set      # TSH: Normal TSH level
        ])
        
        return y0

def simulate_patient(model: QSPModel,
                    t_span: Tuple[float, float] = (0, 168),
                    drug_type: str = 'nivolumab',
                    rtol: float = 1e-6,
                    atol: float = 1e-9) -> pd.DataFrame:
    """
    Simulate a single virtual patient using the QSP model.

    Args:
        model: QSPModel instance
        t_span: Time span for simulation (start, end) in days
        drug_type: Type of checkpoint inhibitor
        rtol: Relative tolerance for ODE solver
        atol: Absolute tolerance for ODE solver

    Returns:
        DataFrame with time course of all state variables
    """

    # Assign immune susceptibility based on drug type if not already set
    if not model.params.susceptibility_assigned:
        model._assign_immune_susceptibility(drug_type)
        model._apply_covariates()  # Re-apply covariates with susceptibility info

    # Get initial conditions
    y0 = model.get_initial_conditions()
    
    # Define time points for output (daily resolution)
    t_eval = np.arange(t_span[0], t_span[1] + 1, 1.0)
    
    # Solve ODE system
    try:
        sol = solve_ivp(
            fun=lambda t, y: model.ode_system(t, y, drug_type),
            t_span=t_span,
            y0=y0,
            t_eval=t_eval,
            method='LSODA',  # Good for stiff systems
            rtol=rtol,
            atol=atol
        )
        
        if not sol.success:
            logger.warning(f"ODE solver failed: {sol.message}")
            
    except Exception as e:
        logger.error(f"Simulation failed: {e}")
        raise
    
    # Create results DataFrame
    results = pd.DataFrame(sol.y.T, columns=model.state_names)
    results['time'] = sol.t
    results['drug_concentration'] = [model.drug_concentration(t, drug_type) for t in sol.t]
    
    # Add derived variables
    results['hypothyroid_grade'] = classify_hypothyroidism(results['TSH'], results['T3'])
    results['thyrocyte_loss_pct'] = (1 - results['Thyro'] / model.params.Thyro_max) * 100
    
    # Reorder columns
    cols = ['time'] + model.state_names + ['drug_concentration', 'hypothyroid_grade', 'thyrocyte_loss_pct']
    results = results[cols]
    
    logger.info(f"Simulation completed: {len(results)} time points")
    return results

def classify_hypothyroidism(TSH: pd.Series, T3: pd.Series) -> pd.Series:
    """
    Classify hypothyroidism severity based on CTCAE v5.0 criteria.
    Literature-based thresholds calibrated for realistic 5-10% incidence rates.

    Normal ranges: TSH 0.4-4.0 mIU/L, T3 3.1-6.8 pmol/L
    Clinical thresholds based on checkpoint inhibitor literature (Barroso-Sousa et al. 2018)

    Args:
        TSH: TSH values in mIU/L
        T3: T3 values in pmol/L

    Returns:
        Series with hypothyroidism grades (0-4)
    """

    grades = pd.Series(0, index=TSH.index)  # Grade 0: Normal

    # Grade 1: Subclinical hypothyroidism - TSH mildly elevated, T3 normal
    # TSH > 4.5 mIU/L AND TSH <= 10 mIU/L AND T3 >= 3.1 pmol/L
    # Based on ATA guidelines for subclinical hypothyroidism
    grades[(TSH > 4.5) & (TSH <= 10.0) & (T3 >= 3.1)] = 1

    # Grade 2: Overt hypothyroidism - TSH clearly elevated OR T3 below normal
    # TSH > 10 mIU/L OR T3 < 3.1 pmol/L (below normal range)
    # Clinically significant requiring intervention
    grades[(TSH > 10.0) | (T3 < 3.1)] = 2

    # Grade 3: Severe hypothyroidism - very high TSH or very low T3
    # TSH > 20 mIU/L OR T3 < 2.5 pmol/L (severe deficiency)
    # Requires immediate medical attention
    grades[(TSH > 20.0) | (T3 < 2.5)] = 3

    # Grade 4: Life-threatening myxedema - extreme values
    # TSH > 50 mIU/L OR T3 < 2.0 pmol/L (myxedema coma range)
    # Life-threatening, requires hospitalization
    grades[(TSH > 50.0) | (T3 < 2.0)] = 4

    return grades

def calculate_risk_score(model: QSPModel, 
                        simulation_results: pd.DataFrame,
                        time_horizon: float = 168) -> Dict[str, float]:
    """
    Calculate personalized risk metrics from simulation results.
    
    Args:
        model: QSPModel instance
        simulation_results: Output from simulate_patient
        time_horizon: Time horizon for risk calculation (days)
        
    Returns:
        Dictionary with risk metrics
    """
    
    # Filter to time horizon
    data = simulation_results[simulation_results['time'] <= time_horizon].copy()
    
    # Primary endpoints
    any_hypothyroid = (data['hypothyroid_grade'] >= 1).any()
    grade2_hypothyroid = (data['hypothyroid_grade'] >= 2).any()
    
    # Time to onset (first occurrence of grade ≥2)
    grade2_times = data[data['hypothyroid_grade'] >= 2]['time']
    time_to_onset = grade2_times.iloc[0] if len(grade2_times) > 0 else np.nan
    
    # Peak values
    peak_TSH = data['TSH'].max()
    min_T3 = data['T3'].min()
    peak_IFN = data['IFN'].max()
    max_thyrocyte_loss = data['thyrocyte_loss_pct'].max()
    
    # Area under curve metrics
    TSH_AUC = np.trapz(data['TSH'], data['time'])
    IFN_AUC = np.trapz(data['IFN'], data['time'])
    
    risk_metrics = {
        'any_hypothyroidism': float(any_hypothyroid),
        'grade2_hypothyroidism': float(grade2_hypothyroid),
        'time_to_onset_days': time_to_onset,
        'time_to_hypothyroidism_days': time_to_onset,  # Alias for backward compatibility
        'peak_TSH_mIU_per_L': peak_TSH,
        'min_T3_pmol_per_L': min_T3,
        'peak_IFN_pg_per_mL': peak_IFN,
        'max_thyrocyte_loss_percent': max_thyrocyte_loss,
        'TSH_AUC': TSH_AUC,
        'IFN_AUC': IFN_AUC,
        'patient_sex_factor': model.params.sex_factor,
        'patient_age_factor': model.params.age_factor,
        'patient_HLA_factor': model.params.HLA_factor,
        'patient_TPO_Ab_titer': model.params.TPO_Ab_titer,
        'immune_susceptible': model.params.immune_susceptible,
        'susceptibility_level': model.params.susceptibility_level
    }
    
    return risk_metrics

# Example usage and testing
if __name__ == "__main__":
    # Create model with default parameters
    model = QSPModel()
    
    # Simulate baseline patient
    print("Simulating baseline patient...")
    results = simulate_patient(model, t_span=(0, 168), drug_type='nivolumab')
    
    # Calculate risk metrics
    risk = calculate_risk_score(model, results)
    
    print(f"Simulation completed:")
    print(f"- Any hypothyroidism: {risk['any_hypothyroidism']}")
    print(f"- Grade ≥2 hypothyroidism: {risk['grade2_hypothyroidism']}")
    print(f"- Time to onset: {risk['time_to_onset_days']:.1f} days")
    print(f"- Peak TSH: {risk['peak_TSH_mIU_per_L']:.1f} mIU/L")
    print(f"- Max thyrocyte loss: {risk['max_thyrocyte_loss_percent']:.1f}%")
    
    # Save results
    results.to_csv('qsp_simulation_results.csv', index=False)
    print("Results saved to 'qsp_simulation_results.csv'")

    # Display first few rows
    print("\nFirst 10 time points:")
    print(results.head(10)[['time', 'T_eff', 'IFN', 'Thyro', 'T3', 'TSH', 'hypothyroid_grade']].round(3))
