# Fix Python configuration first
source("fix_python_config.R")

library(QSPThyroid)

# DIAGNOSTIC: First run a single patient to understand the issue
cat("=== DIAGNOSTIC: Single Patient Analysis ===\n")

# Create a simple baseline patient
model_params <- ModelParameters$new()
qsp_model <- QSPModel$new(model_params)

# Run simulation for a single patient
single_result <- simulate_patient(qsp_model, t_span = c(0, 84), drug_type = 'nivolumab')

# Check TSH trajectory
cat("TSH trajectory (first 10 days):\n")
print(single_result[1:11, c("time", "TSH", "T3", "hypothyroid_grade")])

cat("\nTSH summary statistics:\n")
print(summary(single_result$TSH))

cat("\nHypothyroidism classification summary:\n")
print(table(single_result$hypothyroid_grade))

# Check if any grade 2+ hypothyroidism occurs
grade2_events <- single_result$hypothyroid_grade >= 2
cat("\nGrade 2+ hypothyroidism events:", sum(grade2_events), "out of", length(grade2_events), "time points\n")

if (sum(grade2_events) > 0) {
  first_event_time <- single_result$time[which(grade2_events)[1]]
  first_event_tsh <- single_result$TSH[which(grade2_events)[1]]
  cat("First Grade 2+ event at day", first_event_time, "with TSH =", first_event_tsh, "mIU/L\n")
}

cat("\n=== END DIAGNOSTIC ===\n\n")

# After running the population analysis
results <- run_interactive_analysis(
  mode = "population",
  n_patients = 100,  # Reduced for faster testing
  parallel = TRUE,
  output_dir = "qsp_results"
)

# Extract the population analysis data frame from results
population_data <- results$population

# Check if population data exists and is a data frame
if (is.null(population_data)) {
  stop("Population analysis did not return data. Check if the analysis completed successfully.")
}

# The population analysis returns a list with multiple components
# Let's examine the structure and extract the patient results
cat("Population analysis structure:\n")
cat("Available components:", paste(names(population_data), collapse = ", "), "\n\n")

# Extract the patient results data frame
if ("patient_results" %in% names(population_data)) {
  patient_data <- population_data$patient_results
} else if ("results" %in% names(population_data)) {
  patient_data <- population_data$results
} else {
  # Try to find a data frame in the list
  data_frames <- sapply(population_data, is.data.frame)
  if (any(data_frames)) {
    patient_data <- population_data[[which(data_frames)[1]]]
    cat("Using component:", names(population_data)[which(data_frames)[1]], "\n")
  } else {
    stop("Could not find patient data frame in population results")
  }
}

if (!is.data.frame(patient_data)) {
  stop("Patient data is not a data frame. Expected structure: data.frame, got: ", class(patient_data))
}

cat("Patient analysis completed successfully!\n")
cat("Data dimensions:", nrow(patient_data), "rows x", ncol(patient_data), "columns\n")
cat("Column names:", paste(colnames(patient_data), collapse = ", "), "\n\n")

# Check the structure of the patient data
cat("Patient data structure:\n")
str(patient_data)
cat("\n")

# Show a sample of the data
cat("Sample of patient data:\n")
print(head(patient_data))
cat("\n")

# Map columns to expected names for plotting functions
if (!"thyrotoxicosis_developed" %in% colnames(patient_data)) {
  if ("any_hypothyroidism" %in% colnames(patient_data)) {
    patient_data$thyrotoxicosis_developed <- patient_data$any_hypothyroidism
    cat("Mapped 'any_hypothyroidism' to 'thyrotoxicosis_developed'\n")
  } else if ("grade2_hypothyroidism" %in% colnames(patient_data)) {
    patient_data$thyrotoxicosis_developed <- patient_data$grade2_hypothyroidism
    cat("Mapped 'grade2_hypothyroidism' to 'thyrotoxicosis_developed'\n")
  } else {
    warning("No thyrotoxicosis/hypothyroidism outcome column found")
  }
}

if (!"time_to_onset" %in% colnames(patient_data) && "time_to_onset_days" %in% colnames(patient_data)) {
  patient_data$time_to_onset <- patient_data$time_to_onset_days
  cat("Mapped 'time_to_onset_days' to 'time_to_onset'\n")
}

if (!"risk_score" %in% colnames(patient_data)) {
  # Create a simple risk score based on available variables
  patient_data$risk_score <- with(patient_data, {
    score <- 0
    if ("age" %in% colnames(patient_data)) score <- score + (age - 65) / 20
    if ("TPO_Ab_titer" %in% colnames(patient_data)) score <- score + TPO_Ab_titer / 1000
    if ("HLA_DRB1_03" %in% colnames(patient_data)) score <- score + HLA_DRB1_03 * 0.5
    score
  })
  cat("Created 'risk_score' from available variables\n")
}

cat("\nUpdated column names:", paste(colnames(patient_data), collapse = ", "), "\n\n")

# Check the distribution of outcomes for ROC analysis
cat("Outcome distribution by drug type:\n")
outcome_summary <- table(patient_data$drug_type, patient_data$thyrotoxicosis_developed)
print(outcome_summary)
cat("\n")

# Check risk score distribution
cat("Risk score summary:\n")
print(summary(patient_data$risk_score))
cat("\n")

# Generate all plots
source("R/plot_functions.R")
source("R/advanced_plots.R")
source("R/statistical_plots.R")
source("R/create_dashboard.R")

# Create comprehensive dashboard
plots <- create_analysis_dashboard(patient_data, output_dir = "qsp_results")

# View individual plots
print(plots$incidence_by_drug)
print(plots$time_to_onset)
print(plots$risk_distribution)

# Generate additional custom plots
# Use a more meaningful variable for the custom plot
plot_variable <- if ("peak_TSH_mIU_per_L" %in% colnames(patient_data)) {
  "peak_TSH_mIU_per_L"
} else if ("risk_score" %in% colnames(patient_data)) {
  "risk_score" 
} else {
  "baseline_TSH"
}

plot_title <- switch(plot_variable,
  "peak_TSH_mIU_per_L" = "Peak TSH Distribution by Drug Type and Outcome",
  "risk_score" = "Risk Score Distribution by Drug Type and Outcome",
  "baseline_TSH" = "Baseline TSH Distribution by Drug Type and Outcome"
)

plot_ylabel <- switch(plot_variable,
  "peak_TSH_mIU_per_L" = "Peak TSH (mIU/L)",
  "risk_score" = "Risk Score",
  "baseline_TSH" = "Baseline TSH (mIU/L)"
)

custom_plot <- ggplot(patient_data, aes_string(x = "drug_type", y = plot_variable, fill = "drug_type")) +
  geom_boxplot(alpha = 0.7) +
  geom_jitter(aes(color = factor(thyrotoxicosis_developed)), 
              width = 0.2, alpha = 0.5) +
  scale_fill_brewer(type = "qual", palette = "Set2") +
  scale_color_manual(
    values = c("0" = "blue", "1" = "red"),
    labels = c("0" = "No Event", "1" = "Event"),
    name = "Outcome"
  ) +
  labs(
    title = plot_title,
    x = "Drug Type",
    y = plot_ylabel,
    fill = "Drug Type"
  ) +
  theme_minimal()

print(custom_plot)