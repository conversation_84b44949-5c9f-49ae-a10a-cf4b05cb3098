import random
import numpy as np
from qsp_population_analysis import VirtualCohort

# Test with larger population and different seed
print('Testing with larger population (n=100, seed=999):')
print('='*60)

random.seed(999)
np.random.seed(999)

cohort = VirtualCohort(n_patients=100)
results = cohort.simulate_all()

print('Available columns in results:')
print(results.columns.tolist())

print('\nPopulation characteristics:')
susceptible_count = results['immune_susceptible'].sum()
print(f'Susceptible patients: {susceptible_count}/100 ({susceptible_count/100*100:.1f}%)')

overall_hypo_rate = results['any_hypothyroidism'].mean()
print(f'Overall any hypothyroidism: {overall_hypo_rate*100:.1f}%')

# Check if grade 2+ column exists
if 'grade_2_plus_hypothyroidism' in results.columns:
    overall_grade2_rate = results['grade_2_plus_hypothyroidism'].mean()
    print(f'Overall grade 2+ hypothyroidism: {overall_grade2_rate*100:.1f}%')
elif 'grade_2_hypothyroidism' in results.columns:
    overall_grade2_rate = results['grade_2_hypothyroidism'].mean()
    print(f'Overall grade 2+ hypothyroidism: {overall_grade2_rate*100:.1f}%')
else:
    print('Grade 2+ hypothyroidism column not found')

print()
print('Drug-specific incidence rates:')
for drug in ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']:
    drug_data = results[results['drug_type'] == drug]
    if len(drug_data) > 0:
        drug_any_rate = drug_data['any_hypothyroidism'].mean()
        drug_susceptible = drug_data['immune_susceptible'].sum()
        print(f'{drug:12s}: {drug_any_rate*100:5.1f}% any ({len(drug_data)} patients, {drug_susceptible} susceptible)')

print()
print('Literature targets:')
print('nivolumab   :   8.0% any,   3.5% grade 2+')
print('pembrolizumab:  12.0% any,   5.2% grade 2+')
print('atezolizumab :   5.5% any,   2.1% grade 2+')
print('durvalumab  :   5.0% any,   1.8% grade 2+')

# Check susceptible vs non-susceptible breakdown
print()
print('Susceptibility breakdown:')
susceptible_data = results[results['immune_susceptible'] == True]
non_susceptible_data = results[results['immune_susceptible'] == False]

if len(susceptible_data) > 0:
    susc_any_rate = susceptible_data['any_hypothyroidism'].mean()
    print(f'Susceptible patients ({len(susceptible_data)}): {susc_any_rate*100:.1f}% any hypothyroidism')

if len(non_susceptible_data) > 0:
    non_susc_any_rate = non_susceptible_data['any_hypothyroidism'].mean()
    print(f'Non-susceptible patients ({len(non_susceptible_data)}): {non_susc_any_rate*100:.1f}% any hypothyroidism')
    
    if non_susc_any_rate > 0:
        print('  WARNING: Non-susceptible patients developing hypothyroidism!')
    else:
        print('  Good: No non-susceptible patients developing hypothyroidism')

# Check TSH distribution
print()
print('TSH distribution:')
tsh_values = results['peak_TSH_mIU_per_L']
print(f'  Min TSH: {tsh_values.min():.2f} mIU/L')
print(f'  Max TSH: {tsh_values.max():.2f} mIU/L')
print(f'  Mean TSH: {tsh_values.mean():.2f} mIU/L')
print(f'  Patients with TSH > 4.5: {(tsh_values > 4.5).sum()}/100')
print(f'  Patients with TSH > 10.0: {(tsh_values > 10.0).sum()}/100')
