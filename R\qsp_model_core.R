#' @title QSP Model Core: Checkpoint-Inhibitor-Induced Hypothyroidism
#' @description Core quantitative systems pharmacology (QSP) model for predicting 
#' hypothyroidism induced by PD-1/PD-L1 checkpoint inhibitors.
#' 
#' Mathematical Framework:
#' - 6 coupled ODEs representing immune activation, cytokine release, 
#'   thyrocyte damage, and HPT axis feedback
#' - Patient-specific covariates for personalized risk assessment
#' - Drug-specific pharmacokinetic models for different ICIs
#' 
#' <AUTHOR> Modeling Team
#' @docType package
#' @name qsp_model_core
NULL

#' @title Model Parameters Class
#' @description Container for all QSP model parameters with default values from literature.
#' 
#' Parameters are organized by biological module:
#' - Checkpoint binding and drug PK
#' - T-cell dynamics  
#' - Cytokine kinetics
#' - Thyrocyte population dynamics
#' - Thyroid hormone synthesis
#' - HPT axis feedback
#' - Patient-specific covariates
#' 
#' @export
ModelParameters <- R6::R6Class(
  "ModelParameters",
  public = list(
    
    #' @field Kd_PD1_PDL1 PD-1/PD-L1 dissociation constant (M)
    Kd_PD1_PDL1 = 8e-9,
    
    #' @field k_on_PD1 Association rate (M⁻¹s⁻¹)
    k_on_PD1 = 1e5,
    
    #' @field k_off_PD1 Dissociation rate (s⁻¹)
    k_off_PD1 = 0.0008,
    
    #' @field IC50_block Fractional occupancy for 50% T-cell activation
    IC50_block = 0.35,
    
    # Drug-specific binding affinities (nM)
    #' @field Kd_nivo_PD1 Nivolumab-PD-1 binding affinity (nM)
    Kd_nivo_PD1 = 2.6,
    
    #' @field Kd_pembro_PD1 Pembrolizumab-PD-1 binding affinity (nM)
    Kd_pembro_PD1 = 3.5,  # Adjusted for clinical ranking
    
    #' @field Kd_atezo_PDL1 Atezolizumab-PD-L1 binding affinity (nM)
    Kd_atezo_PDL1 = 0.4,
    
    # T-cell Dynamics - CORRECTED for realistic immune response
    #' @field alpha APC-driven expansion rate (day⁻¹) - FINAL TUNING for drug-specific effects
    alpha = 8e-4,  # Final tuning to allow drug-specific differentiation

    #' @field beta Natural death/exhaustion rate (day⁻¹) - FINAL TUNING for drug-specific effects
    beta = 0.18,   # Final tuning T-cell death for drug-specific effects

    #' @field gamma PD-1 mediated inhibition (day⁻¹) - FINAL TUNING for drug-specific effects
    gamma = 1.0,
    
    #' @field delta IL-2 driven proliferation (day⁻¹)
    delta = 0.01,
    
    #' @field T_eff0 Baseline autoreactive T-cells (cells/L)
    T_eff0 = 2e3,
    
    # Cytokine Parameters - EXTREMELY CONSERVATIVE for realistic incidence rates (10-20%)
    #' @field epsilon IFN-γ secretion rate (pg cell⁻¹ day⁻¹) - EXTREMELY CONSERVATIVE
    epsilon = 0.05,  # EXTREMELY CONSERVATIVE: reduced by 83% from original 0.3

    #' @field k_clear_IFN IFN-γ clearance rate (day⁻¹) - EXTREMELY CONSERVATIVE
    k_clear_IFN = 20.0,  # EXTREMELY CONSERVATIVE: increased by 150% for very fast clearance

    #' @field EC50_IFN_death IFN-γ potency on thyrocyte death (pg/mL) - EXTREMELY CONSERVATIVE
    EC50_IFN_death = 500,  # EXTREMELY CONSERVATIVE: increased 5x for very high threshold

    #' @field Hill_IFN Hill coefficient for IFN-γ dose-response
    Hill_IFN = 1.2,

    # Thyrocyte Dynamics - EXTREMELY CONSERVATIVE for realistic hypothyroidism incidence (10-20%)
    #' @field k_death Apoptosis rate per IFN-γ (day⁻¹(pg/mL)⁻¹) - EXTREMELY CONSERVATIVE
    k_death = 0.001,  # EXTREMELY CONSERVATIVE: reduced by 87.5% from original 0.008

    #' @field k_regen Regeneration rate (day⁻¹) - unchanged
    k_regen = 0.06,   # Regeneration rate unchanged
    
    #' @field Thyro_max Normalized maximum thyroid mass
    Thyro_max = 1.0,
    
    # Thyroid Hormone Synthesis
    #' @field k_syn_T3 T3 synthesis rate (pmol L⁻¹ day⁻¹ %⁻¹)
    k_syn_T3 = 2.5,
    
    #' @field k_syn_T4 T4 synthesis rate (pmol L⁻¹ day⁻¹ %⁻¹)
    k_syn_T4 = 15.0,
    
    #' @field k_deg_T3 T3 degradation rate (day⁻¹)
    k_deg_T3 = 0.693,
    
    #' @field k_deg_T4 T4 degradation rate (day⁻¹)
    k_deg_T4 = 0.099,
    
    # HPT Axis Parameters
    #' @field TSH_set TSH setpoint (mIU/L)
    TSH_set = 1.5,
    
    #' @field T3_set T3 setpoint (pmol/L)
    T3_set = 4.8,
    
    #' @field T4_set T4 setpoint (pmol/L)
    T4_set = 12.0,
    
    #' @field theta TSH feedback gain (day⁻¹(pmol/L)⁻¹)
    theta = 0.1,
    
    #' @field k_metab_TSH TSH clearance rate (day⁻¹)
    k_metab_TSH = 0.05,
    
    # Patient Covariates (multiplicative factors)
    #' @field sex_factor Female vs male risk multiplier
    sex_factor = 1.0,

    #' @field age_factor Age >60 years multiplier
    age_factor = 1.0,

    #' @field HLA_factor HLA-DRB1*03:01 carrier multiplier
    HLA_factor = 1.0,

    #' @field TPO_Ab_titer Anti-TPO antibody titer (log10(IU/mL))
    TPO_Ab_titer = 0.0,

    # Immune Susceptibility State - NEW ARCHITECTURE
    #' @field immune_susceptible TRUE for patients who can develop immune responses
    immune_susceptible = FALSE,

    #' @field susceptibility_level "none", "low", or "high"
    susceptibility_level = "none",

    #' @field susceptibility_assigned Flag to track if susceptibility has been assigned
    susceptibility_assigned = FALSE,

    # Activation Thresholds - NEW ARCHITECTURE
    #' @field drug_threshold_ng_ml Minimum drug concentration for immune activation
    drug_threshold_ng_ml = 50000.0,

    #' @field cytokine_threshold_pg_ml Minimum IFN-γ for thyrocyte damage
    cytokine_threshold_pg_ml = 1000.0,  # EXTREMELY CONSERVATIVE: 10x higher threshold

    #' @field cumulative_exposure_threshold ng*day/mL for sustained exposure
    cumulative_exposure_threshold = 500000.0,
    
    # Drug PK Parameters
    #' @field drug_clearance Drug clearance (L/day)
    drug_clearance = 0.2,
    
    #' @field drug_volume Drug volume of distribution (L)
    drug_volume = 6.0,
    
    #' @field drug_dose Drug dose (mg)
    drug_dose = 240,
    
    #' @field dosing_interval Dosing interval (days)
    dosing_interval = 14,
    
    #' @description Initialize ModelParameters
    #' @param ... Named parameters to override defaults
    initialize = function(...) {
      args <- list(...)
      for (name in names(args)) {
        if (name %in% names(self)) {
          self[[name]] <- args[[name]]
        }
      }
    },
    
    #' @description Print parameter summary
    print = function() {
      cat("QSP Model Parameters:\n")
      cat("- Checkpoint binding: Kd_PD1_PDL1 =", self$Kd_PD1_PDL1, "M\n")
      cat("- T-cell dynamics: alpha =", self$alpha, "day⁻¹\n")
      cat("- Cytokine kinetics: epsilon =", self$epsilon, "pg cell⁻¹ day⁻¹\n")
      cat("- Thyrocyte dynamics: k_death =", self$k_death, "day⁻¹(pg/mL)⁻¹\n")
      cat("- HPT axis: TSH_set =", self$TSH_set, "mIU/L\n")
      cat("- Patient covariates: sex_factor =", self$sex_factor, "\n")
    }
  )
)

#' @title QSP Model Class
#' @description Main QSP model class implementing the ODE system for
#' checkpoint-inhibitor-induced hypothyroidism.
#'
#' The model consists of 6 state variables:
#' 1. R: PD-1/PD-L1 complex concentration
#' 2. T_eff: Activated autoreactive CD8+ T cells
#' 3. IFN: Interferon-gamma concentration
#' 4. Thyro: Functional thyrocyte biomass (%)
#' 5. T3: Triiodothyronine concentration
#' 6. TSH: Thyroid-stimulating hormone
#'
#' @export
QSPModel <- R6::R6Class(
  "QSPModel",
  public = list(

    #' @field params ModelParameters object
    params = NULL,

    #' @field state_names Names of state variables
    state_names = c('R', 'T_eff', 'IFN', 'Thyro', 'T3', 'TSH'),

    #' @field n_states Number of state variables
    n_states = 6,

    #' @description Initialize QSP model
    #' @param params ModelParameters object. If NULL, uses default values.
    initialize = function(params = NULL) {
      if (is.null(params)) {
        self$params <- ModelParameters$new()
      } else {
        self$params <- params
      }

      # Note: immune susceptibility will be assigned in simulate_patient based on drug_type

      futile.logger::flog.info("QSP Model initialized with %d state variables", self$n_states)
    },

    #' @description Assign immune susceptibility based on population heterogeneity
    #' @param drug_type Type of checkpoint inhibitor
    assign_immune_susceptibility = function(drug_type = 'nivolumab') {
      # Drug-specific susceptibility rates (EXTREMELY CONSERVATIVE for realistic clinical incidence)
      drug_susceptibility_rates <- list(
        'nivolumab' = 0.08,      # 8% EXTREMELY CONSERVATIVE (was 15%)
        'pembrolizumab' = 0.06,  # 6% EXTREMELY CONSERVATIVE (was 12%)
        'atezolizumab' = 0.04,   # 4% EXTREMELY CONSERVATIVE (was 8%)
        'durvalumab' = 0.03      # 3% EXTREMELY CONSERVATIVE (was 6%)
      )

      total_susceptible_rate <- drug_susceptibility_rates[[drug_type]]
      if (is.null(total_susceptible_rate)) total_susceptible_rate <- 0.12

      # Assign susceptibility level based on random draw
      rand_val <- runif(1)

      # Calculate thresholds - ADJUSTED for more realistic distribution
      non_susceptible_threshold <- 1.0 - total_susceptible_rate
      low_susceptible_threshold <- non_susceptible_threshold + total_susceptible_rate * 0.8

      if (rand_val < non_susceptible_threshold) {
        # Non-susceptible (85-95% depending on drug)
        self$params$immune_susceptible <- FALSE
        self$params$susceptibility_level <- "none"
      } else if (rand_val < low_susceptible_threshold) {
        # Low-susceptible (80% of susceptible patients - more conservative)
        self$params$immune_susceptible <- TRUE
        self$params$susceptibility_level <- "low"
      } else {
        # High-susceptible (20% of susceptible patients - reduced from 30%)
        self$params$immune_susceptible <- TRUE
        self$params$susceptibility_level <- "high"
      }

      # Mark susceptibility as assigned
      self$params$susceptibility_assigned <- TRUE
    },

    #' @description Calculate drug concentration at time t
    #' @param t Time in days
    #' @param drug_type Type of checkpoint inhibitor
    #' @return Drug concentration in ng/mL
    drug_concentration = function(t, drug_type = 'nivolumab') {
      p <- self$params

      # Adjust PK parameters based on drug type - REALISTIC clinical doses
      if (drug_type == 'nivolumab') {
        CL <- 0.2; V <- 6.0; dose <- 3  # L/day, L, mg/kg
      } else if (drug_type == 'pembrolizumab') {
        CL <- 0.22; V <- 6.0; dose <- 2  # 2 mg/kg
      } else if (drug_type == 'atezolizumab') {
        CL <- 0.25; V <- 6.5; dose <- 15  # 15 mg/kg
      } else if (drug_type == 'durvalumab') {
        CL <- 0.24; V <- 6.2; dose <- 10  # 10 mg/kg
      } else {
        CL <- p$drug_clearance; V <- p$drug_volume; dose <- p$drug_dose
      }

      # Convert mg/kg to total dose for 70kg patient
      dose <- dose * 70  # mg

      # Multiple dose superposition - CORRECTED unit conversion
      conc <- 0.0
      dose_times <- seq(0, t + p$dosing_interval, by = p$dosing_interval)

      for (dose_time in dose_times) {
        if (dose_time <= t) {
          time_since_dose <- t - dose_time
          # Convert mg to ng/mL: mg * 1e6 ng/mg / L * 1000 mL/L = mg * 1e9 / V(mL)
          conc <- conc + (dose * 1e6 / (V * 1000)) * exp(-CL * time_since_dose / V)
        }
      }

      return(max(conc, 0.0))
    },

    #' @description Calculate fractional PD-1/PD-L1 complex formation
    #' @param drug_conc Drug concentration in ng/mL
    #' @param drug_type Type of checkpoint inhibitor
    #' @return Fractional complex formation (0-1)
    checkpoint_binding = function(drug_conc, drug_type = 'nivolumab') {
      p <- self$params

      # Convert ng/mL to nM (approximate MW = 150 kDa)
      drug_conc_nM <- drug_conc / 150

      # Drug-specific binding affinity and immune activation potency
      if (drug_type == 'nivolumab') {
        Kd_drug <- p$Kd_nivo_PD1
        immune_potency <- 1.0  # Reference drug
      } else if (drug_type == 'pembrolizumab') {
        Kd_drug <- p$Kd_pembro_PD1
        immune_potency <- 0.8  # Second most immunogenic after nivolumab
      } else if (drug_type == 'atezolizumab') {
        Kd_drug <- p$Kd_atezo_PDL1
        immune_potency <- 0.05  # Much less immunogenic (literature-based)
      } else if (drug_type == 'durvalumab') {
        Kd_drug <- p$Kd_atezo_PDL1  # Similar to atezolizumab (PD-L1 inhibitor)
        immune_potency <- 0.10  # Lower than pembrolizumab for correct ranking
      } else {
        Kd_drug <- p$Kd_nivo_PD1  # Default
        immune_potency <- 1.0
      }

      # Simplified binding model - fractional checkpoint inhibition
      # Convert to more realistic scale
      drug_conc_nM <- drug_conc_nM * 1e-6  # Convert to µM scale for realistic binding

      # Simple competitive inhibition model
      f_drug <- drug_conc_nM / (Kd_drug + drug_conc_nM)

      # Baseline checkpoint signaling (normalized to 1.0)
      baseline_checkpoint <- 1.0

      # Drug-specific immune activation potential (higher = more immunogenic)
      # When f_drug is high (good binding), immune activation depends on drug-specific potency
      immune_activation <- f_drug * immune_potency

      return(immune_activation)
    }
  ),

  private = list(
    #' @description Apply patient-specific covariate effects based on immune susceptibility
    apply_covariates = function() {
      p <- self$params

      # Only apply parameter modifications if patient is immune susceptible
      if (!p$immune_susceptible) {
        # Non-susceptible patients: minimal parameter changes
        return()
      }

      # Calculate overall risk multiplier from all factors for susceptible patients
      overall_risk <- p$sex_factor * p$age_factor * p$HLA_factor
      tpo_effect <- (1 + 0.8 * p$TPO_Ab_titer)

      # Apply susceptibility-level-specific parameter sets - EXTREMELY CONSERVATIVE
      if (p$susceptibility_level == "low") {
        # Low-susceptible: extremely mild immune response parameters
        p$k_death <- p$k_death * 0.1 * overall_risk * tpo_effect  # Reduced from 0.3 to 0.1
        p$epsilon <- p$epsilon * 0.05  # Extremely reduced cytokine production (was 0.2)
        p$EC50_IFN_death <- p$EC50_IFN_death * 5.0  # Much higher threshold (was 3.0)
        p$T_eff0 <- p$T_eff0 * 0.1 * overall_risk  # Reduced from 0.3 to 0.1

      } else if (p$susceptibility_level == "high") {
        # High-susceptible: extremely mild immune response parameters (ultra conservative)
        p$k_death <- p$k_death * 0.5 * overall_risk * tpo_effect  # Reduced from 1.0 to 0.5
        p$epsilon <- p$epsilon * 0.2  # Extremely reduced cytokine production (was 0.5)
        p$EC50_IFN_death <- p$EC50_IFN_death * 2.0  # Higher threshold (was 1.2)
        p$T_eff0 <- p$T_eff0 * 0.8 * overall_risk  # Reduced from 1.5 to 0.8
      }

      # Adjust thyroid reserve based on susceptibility
      if (p$susceptibility_level %in% c("low", "high")) {
        p$Thyro_max <- p$Thyro_max / (overall_risk^0.2)
      }

      futile.logger::flog.debug("Applied covariates: susceptible=%s, level=%s, overall_risk=%f",
                               p$immune_susceptible, p$susceptibility_level, overall_risk)
    }
  )
)

#' @title ODE System for QSP Model
#' @description Define the system of ODEs for the QSP model
#' @param t Time in days
#' @param y State vector [R, T_eff, IFN, Thyro, T3, TSH]
#' @param parms List containing model object and drug_type
#' @return Derivative vector dy/dt
ode_system <- function(t, y, parms) {
  model <- parms$model
  drug_type <- parms$drug_type
  p <- model$params

  R <- y[1]; T_eff <- y[2]; IFN <- y[3]; Thyro <- y[4]; T3 <- y[5]; TSH <- y[6]

  # Get current drug concentration
  drug_conc <- model$drug_concentration(t, drug_type)

  # Checkpoint binding dynamics (steady-state approximation)
  R_new <- model$checkpoint_binding(drug_conc, drug_type)
  dR_dt <- 0  # Assume fast equilibrium

  # T-cell dynamics with immune susceptibility
  APC_baseline <- 1e6  # cells/L, constant APC population
  IL2_baseline <- 0.1  # ng/mL, basal IL-2 level

  # Drug-specific activation thresholds (ng/mL) - EXTREMELY CONSERVATIVE for realistic incidence
  drug_thresholds <- list(
    'nivolumab' = 150000.0,     # PD-1 inhibitor - EXTREMELY CONSERVATIVE for ~15% incidence
    'pembrolizumab' = 180000.0, # PD-1 inhibitor - EXTREMELY CONSERVATIVE for ~12% incidence
    'atezolizumab' = 500000.0,  # PD-L1 inhibitor - EXTREMELY CONSERVATIVE for ~8% incidence
    'durvalumab' = 600000.0     # PD-L1 inhibitor - EXTREMELY CONSERVATIVE for ~6% incidence
  )

  activation_threshold <- ifelse(is.null(drug_thresholds[[drug_type]]),
                                p$drug_threshold_ng_ml,
                                drug_thresholds[[drug_type]])

  if (p$immune_susceptible && drug_conc >= activation_threshold) {
    # Only susceptible patients with sufficient drug exposure activate T-cells
    dT_eff_dt <- (p$alpha * APC_baseline * (1 + p$gamma * R_new) -
                  p$beta * T_eff +
                  p$delta * IL2_baseline * T_eff)
  } else {
    # Non-susceptible patients or insufficient drug exposure: T-cell decay only
    dT_eff_dt <- -p$beta * T_eff
  }

  # Cytokine dynamics with immune susceptibility and activation thresholds
  if (p$immune_susceptible && drug_conc >= activation_threshold) {
    # Only susceptible patients with sufficient drug exposure produce cytokines
    dIFN_dt <- p$epsilon * T_eff - p$k_clear_IFN * IFN
  } else {
    # Non-susceptible patients or insufficient drug exposure: no cytokine production
    dIFN_dt <- -p$k_clear_IFN * IFN
  }

  # Thyrocyte dynamics with cytokine threshold for damage
  if (IFN >= p$cytokine_threshold_pg_ml) {
    IFN_effect <- IFN^p$Hill_IFN / (p$EC50_IFN_death^p$Hill_IFN + IFN^p$Hill_IFN)
    thyrocyte_death <- p$k_death * IFN_effect * Thyro
  } else {
    # Below cytokine threshold: no thyrocyte damage
    thyrocyte_death <- 0.0
  }

  dThyro_dt <- -thyrocyte_death + p$k_regen * (p$Thyro_max - Thyro)

  # Thyroid hormone synthesis
  dT3_dt <- p$k_syn_T3 * Thyro - p$k_deg_T3 * T3

  # HPT axis feedback
  T3_error <- p$T3_set - T3
  TSH_error <- TSH - p$TSH_set
  dTSH_dt <- p$theta * T3_error - p$k_metab_TSH * TSH_error

  return(list(c(dR_dt, dT_eff_dt, dIFN_dt, dThyro_dt, dT3_dt, dTSH_dt)))
}

#' @title Get Initial Conditions
#' @description Get physiological initial conditions for the ODE system
#' @param model QSPModel object
#' @return Initial state vector
#' @export
initial_conditions <- function(model) {
  p <- model$params

  # Steady-state initial conditions
  y0 <- c(
    0.0,           # R: No drug initially
    p$T_eff0,      # T_eff: Baseline autoreactive T-cells
    0.0,           # IFN: No cytokine initially
    p$Thyro_max,   # Thyro: Full thyroid mass
    p$T3_set,      # T3: Normal hormone level
    p$TSH_set      # TSH: Normal TSH level
  )

  names(y0) <- model$state_names
  return(y0)
}

#' @title Simulate Patient
#' @description Simulate a single virtual patient using the QSP model
#' @param model QSPModel instance
#' @param t_span Time span for simulation (start, end) in days
#' @param drug_type Type of checkpoint inhibitor
#' @param rtol Relative tolerance for ODE solver
#' @param atol Absolute tolerance for ODE solver
#' @return Data frame with time course of all state variables
#' @export
simulate_patient <- function(model,
                             t_span = c(0, 168),
                             drug_type = 'nivolumab',
                             rtol = 1e-6,
                             atol = 1e-9) {

  # Assign immune susceptibility based on drug type if not already set
  if (!model$params$susceptibility_assigned) {
    model$assign_immune_susceptibility(drug_type)
    # Re-apply covariates with susceptibility info
    model$params <- model$params  # Trigger any necessary updates
  }

  # Get initial conditions
  y0 <- initial_conditions(model)

  # Define time points for output (daily resolution)
  times <- seq(t_span[1], t_span[2], by = 1.0)

  # Parameters for ODE solver
  parms <- list(model = model, drug_type = drug_type)

  # Solve ODE system
  tryCatch({
    sol <- deSolve::ode(
      y = y0,
      times = times,
      func = ode_system,
      parms = parms,
      method = "lsoda",  # Good for stiff systems
      rtol = rtol,
      atol = atol
    )

    # Convert to data frame
    results <- as.data.frame(sol)
    names(results) <- c('time', model$state_names)

    # Add drug concentration
    results$drug_concentration <- sapply(results$time, function(t) {
      model$drug_concentration(t, drug_type)
    })

    # Add derived variables
    results$hypothyroid_grade <- classify_hypothyroidism(results$TSH, results$T3)
    results$thyrocyte_loss_pct <- (1 - results$Thyro / model$params$Thyro_max) * 100

    futile.logger::flog.info("Simulation completed: %d time points", nrow(results))
    return(results)

  }, error = function(e) {
    futile.logger::flog.error("Simulation failed: %s", e$message)
    stop(e)
  })
}

#' @title Classify Hypothyroidism
#' @description Classify hypothyroidism severity based on CTCAE v5.0 criteria
#' Literature-based thresholds calibrated for realistic 5-10% incidence rates
#' Normal ranges: TSH 0.4-4.0 mIU/L, T3 3.1-6.8 pmol/L
#' Clinical thresholds based on checkpoint inhibitor literature (Barroso-Sousa et al. 2018)
#' @param TSH TSH values in mIU/L
#' @param T3 T3 values in pmol/L
#' @return Vector with hypothyroidism grades (0-4)
#' @export
classify_hypothyroidism <- function(TSH, T3) {
  grades <- rep(0, length(TSH))  # Grade 0: Normal

  # Grade 1: Subclinical hypothyroidism - TSH mildly elevated, T3 normal
  # TSH > 4.5 mIU/L AND TSH <= 10 mIU/L AND T3 >= 3.1 pmol/L
  # Based on ATA guidelines for subclinical hypothyroidism
  grades[(TSH > 4.5) & (TSH <= 10.0) & (T3 >= 3.1)] <- 1

  # Grade 2: Overt hypothyroidism - TSH clearly elevated OR T3 below normal
  # TSH > 10 mIU/L OR T3 < 3.1 pmol/L (below normal range)
  # Clinically significant requiring intervention
  grades[(TSH > 10.0) | (T3 < 3.1)] <- 2

  # Grade 3: Severe hypothyroidism - very high TSH or very low T3
  # TSH > 20 mIU/L OR T3 < 2.5 pmol/L (severe deficiency)
  # Requires immediate medical attention
  grades[(TSH > 20.0) | (T3 < 2.5)] <- 3

  # Grade 4: Life-threatening myxedema - extreme values
  # TSH > 50 mIU/L OR T3 < 2.0 pmol/L (myxedema coma range)
  # Life-threatening, requires hospitalization
  grades[(TSH > 50.0) | (T3 < 2.0)] <- 4

  return(grades)
}

#' @title Calculate Risk Score
#' @description Calculate personalized risk metrics from simulation results
#' @param model QSPModel instance
#' @param simulation_results Output from simulate_patient
#' @param time_horizon Time horizon for risk calculation (days)
#' @return List with risk metrics
#' @export
calculate_risk_score <- function(model,
                                 simulation_results,
                                 time_horizon = 168) {

  # Filter to time horizon
  data <- simulation_results[simulation_results$time <= time_horizon, ]

  # Primary endpoints
  any_hypothyroid <- any(data$hypothyroid_grade >= 1)
  grade2_hypothyroid <- any(data$hypothyroid_grade >= 2)

  # Time to onset (first occurrence of grade ≥2)
  grade2_times <- data$time[data$hypothyroid_grade >= 2]
  time_to_onset <- if (length(grade2_times) > 0) grade2_times[1] else NA

  # Peak values
  peak_TSH <- max(data$TSH)
  peak_IFN <- max(data$IFN)
  max_thyrocyte_loss <- max(data$thyrocyte_loss_pct)

  # Area under curve metrics
  TSH_AUC <- pracma::trapz(data$time, data$TSH)
  IFN_AUC <- pracma::trapz(data$time, data$IFN)

  risk_metrics <- list(
    any_hypothyroidism = as.numeric(any_hypothyroid),
    grade2_hypothyroidism = as.numeric(grade2_hypothyroid),
    time_to_onset_days = time_to_onset,
    peak_TSH_mIU_per_L = peak_TSH,
    peak_IFN_pg_per_mL = peak_IFN,
    max_thyrocyte_loss_percent = max_thyrocyte_loss,
    TSH_AUC = TSH_AUC,
    IFN_AUC = IFN_AUC,
    patient_sex_factor = model$params$sex_factor,
    patient_age_factor = model$params$age_factor,
    patient_HLA_factor = model$params$HLA_factor,
    patient_TPO_Ab_titer = model$params$TPO_Ab_titer
  )

  return(risk_metrics)
}

#' @title Drug Concentration (Standalone Function)
#' @description Calculate drug concentration at time t using one-compartment PK model
#' @param t Time in days
#' @param drug_type Type of checkpoint inhibitor
#' @param dosing_interval Dosing interval in days
#' @return Drug concentration in ng/mL
#' @export
drug_concentration <- function(t, drug_type = 'nivolumab', dosing_interval = 14) {
  # Adjust PK parameters based on drug type - REALISTIC clinical doses
  if (drug_type == 'nivolumab') {
    CL <- 0.2; V <- 6.0; dose <- 3  # L/day, L, mg/kg
  } else if (drug_type == 'pembrolizumab') {
    CL <- 0.22; V <- 6.0; dose <- 2  # 2 mg/kg
  } else if (drug_type == 'atezolizumab') {
    CL <- 0.25; V <- 6.5; dose <- 15  # 15 mg/kg
  } else {
    stop("Unknown drug type: ", drug_type)
  }

  # Convert mg/kg to total dose for 70kg patient
  dose <- dose * 70  # mg

  # Multiple dose superposition - CORRECTED unit conversion
  conc <- 0.0
  dose_times <- seq(0, t + dosing_interval, by = dosing_interval)

  for (dose_time in dose_times) {
    if (dose_time <= t) {
      time_since_dose <- t - dose_time
      # Convert mg to ng/mL: mg * 1e6 ng/mg / L * 1000 mL/L = mg * 1e9 / V(mL)
      conc <- conc + (dose * 1e6 / (V * 1000)) * exp(-CL * time_since_dose / V)
    }
  }

  return(max(conc, 0.0))
}

#' @title Checkpoint Binding (Standalone Function)
#' @description Calculate fractional PD-1/PD-L1 complex formation after drug binding
#' @param drug_conc Drug concentration in ng/mL
#' @param drug_type Type of checkpoint inhibitor
#' @return Fractional complex formation (0-1)
#' @export
checkpoint_binding <- function(drug_conc, drug_type = 'nivolumab') {
  # Drug-specific binding affinities (nM)
  Kd_nivo_PD1 <- 2.6
  Kd_pembro_PD1 <- 0.28
  Kd_atezo_PDL1 <- 0.4
  Kd_PD1_PDL1 <- 8.0  # nM

  # Convert ng/mL to nM (approximate MW = 150 kDa)
  drug_conc_nM <- drug_conc / 150

  # Drug-specific binding affinity
  if (drug_type == 'nivolumab') {
    Kd_drug <- Kd_nivo_PD1
  } else if (drug_type == 'pembrolizumab') {
    Kd_drug <- Kd_pembro_PD1
  } else if (drug_type == 'atezolizumab') {
    Kd_drug <- Kd_atezo_PDL1
  } else {
    Kd_drug <- Kd_nivo_PD1  # Default
  }

  # Competitive binding model
  PD1_total <- 1e-9  # M, typical surface concentration
  PDL1_total <- 1e-9  # M

  # Fractional occupancy by drug
  f_drug <- drug_conc_nM / (Kd_drug + drug_conc_nM + PDL1_total * Kd_drug / Kd_PD1_PDL1)

  # Remaining PD-1/PD-L1 complex formation
  return((1 - f_drug) * PD1_total * PDL1_total / (Kd_PD1_PDL1 + PDL1_total))
}

