"""
QSP Thyroid Model - Comprehensive Plotting Functions

This module provides publication-quality plotting functions for the QSP thyroid model
with calibrated parameters (k_death=0.030, EC50_IFN_death=100) that produce realistic
hypothyroidism incidence rates (0-15%).

Author: QSP Modeling Team
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')

# Set publication-quality plotting parameters
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'figure.dpi': 300,
    'font.size': 12,
    'font.family': 'Arial',
    'axes.linewidth': 1.5,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'xtick.major.size': 6,
    'ytick.major.size': 6,
    'legend.frameon': False,
    'legend.fontsize': 10
})

# Define consistent color palette
COLORS = {
    'tsh': '#2E86AB',      # Blue for TSH
    't3': '#A23B72',       # Purple for T3
    'drug': '#F18F01',     # Orange for drug concentrations
    'cytokine': '#C73E1D', # Red for cytokines
    'thyrocyte': '#4CAF50', # Green for thyrocytes
    'grade1': '#FFC107',   # Amber for Grade 1
    'grade2': '#FF5722',   # Deep orange for Grade 2+
    'normal': '#4CAF50'    # Green for normal
}

def plot_tsh_timecourse(results, patient_id=None, save_path=None, show_grades=True):
    """
    Plot TSH progression with CTCAE grade thresholds.
    
    Parameters:
    -----------
    results : pd.DataFrame or dict
        Simulation results containing time and TSH columns
    patient_id : str, optional
        Patient ID for title (if applicable)
    save_path : str, optional
        Path to save the plot
    show_grades : bool
        Whether to show CTCAE grade threshold lines
        
    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Extract data
    if isinstance(results, dict):
        time = results['time']
        tsh = results['TSH']
    else:
        time = results['time'] if 'time' in results.columns else results.index
        tsh = results['TSH']
    
    # Convert time to weeks for better readability
    time_weeks = np.array(time) / 7
    
    # Plot TSH timecourse
    ax.plot(time_weeks, tsh, color=COLORS['tsh'], linewidth=3, label='TSH')
    
    if show_grades:
        # Add CTCAE grade threshold lines
        ax.axhline(y=4.5, color=COLORS['grade1'], linestyle='--', alpha=0.7, 
                  label='Grade 1 threshold (4.5 mIU/L)')
        ax.axhline(y=10.0, color=COLORS['grade2'], linestyle='--', alpha=0.7,
                  label='Grade 2 threshold (10.0 mIU/L)')
        
        # Add normal range shading
        ax.axhspan(0.4, 4.5, alpha=0.1, color=COLORS['normal'], label='Normal range')
    
    # Formatting
    ax.set_xlabel('Time (weeks)', fontsize=14, fontweight='bold')
    ax.set_ylabel('TSH (mIU/L)', fontsize=14, fontweight='bold')
    
    title = 'TSH Progression Over Time'
    if patient_id:
        title += f' - Patient {patient_id}'
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    
    ax.legend(loc='upper left')
    ax.grid(True, alpha=0.3)
    
    # Set reasonable y-axis limits
    max_tsh = np.max(tsh)
    ax.set_ylim(0, max(12, max_tsh * 1.1))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig

def plot_t3_timecourse(results, patient_id=None, save_path=None, show_normal_range=True):
    """
    Plot T3 levels with normal range and hypothyroidism threshold.
    
    Parameters:
    -----------
    results : pd.DataFrame or dict
        Simulation results containing time and T3 columns
    patient_id : str, optional
        Patient ID for title
    save_path : str, optional
        Path to save the plot
    show_normal_range : bool
        Whether to show normal T3 range
        
    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Extract data
    if isinstance(results, dict):
        time = results['time']
        t3 = results['T3']
    else:
        time = results['time'] if 'time' in results.columns else results.index
        t3 = results['T3']
    
    time_weeks = np.array(time) / 7
    
    # Plot T3 timecourse
    ax.plot(time_weeks, t3, color=COLORS['t3'], linewidth=3, label='T3')
    
    if show_normal_range:
        # Normal T3 range: 3.1-6.8 pmol/L
        ax.axhspan(3.1, 6.8, alpha=0.1, color=COLORS['normal'], label='Normal range (3.1-6.8 pmol/L)')
        ax.axhline(y=3.1, color=COLORS['grade1'], linestyle='--', alpha=0.7,
                  label='Hypothyroidism threshold (<3.1 pmol/L)')
    
    # Formatting
    ax.set_xlabel('Time (weeks)', fontsize=14, fontweight='bold')
    ax.set_ylabel('T3 (pmol/L)', fontsize=14, fontweight='bold')
    
    title = 'T3 Levels Over Time'
    if patient_id:
        title += f' - Patient {patient_id}'
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    
    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)
    
    # Set reasonable y-axis limits
    min_t3, max_t3 = np.min(t3), np.max(t3)
    ax.set_ylim(max(0, min_t3 * 0.9), max_t3 * 1.1)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig

def plot_drug_pk_profile(results, drug_type='nivolumab', patient_id=None, save_path=None):
    """
    Plot drug concentration over 12-week treatment cycles with therapeutic thresholds.
    
    Parameters:
    -----------
    results : pd.DataFrame or dict
        Simulation results containing time and drug concentration
    drug_type : str
        Type of checkpoint inhibitor drug
    patient_id : str, optional
        Patient ID for title
    save_path : str, optional
        Path to save the plot
        
    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Extract data
    if isinstance(results, dict):
        time = results['time']
        drug_conc = results.get('drug_concentration', results.get('C_drug', np.zeros_like(time)))
    else:
        time = results['time'] if 'time' in results.columns else results.index
        drug_conc = results.get('drug_concentration', results.get('C_drug', np.zeros(len(time))))
    
    time_weeks = np.array(time) / 7
    
    # Convert to μg/mL for better readability
    drug_conc_ug = np.array(drug_conc) / 1000  # ng/mL to μg/mL
    
    # Plot drug concentration
    ax.plot(time_weeks, drug_conc_ug, color=COLORS['drug'], linewidth=3, 
           label=f'{drug_type.capitalize()} concentration')
    
    # Add therapeutic threshold (50 μg/mL = 50,000 ng/mL)
    ax.axhline(y=50, color='red', linestyle='--', alpha=0.7,
              label='Activation threshold (50 μg/mL)')
    
    # Add dosing schedule markers (every 2 weeks for most drugs)
    dosing_weeks = np.arange(0, max(time_weeks), 2)
    for week in dosing_weeks:
        ax.axvline(x=week, color='gray', linestyle=':', alpha=0.5)
    
    # Formatting
    ax.set_xlabel('Time (weeks)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Drug Concentration (μg/mL)', fontsize=14, fontweight='bold')
    
    title = f'{drug_type.capitalize()} Pharmacokinetic Profile'
    if patient_id:
        title += f' - Patient {patient_id}'
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    
    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)
    
    # Set y-axis to show relevant range
    max_conc = np.max(drug_conc_ug) if len(drug_conc_ug) > 0 else 100
    ax.set_ylim(0, max(100, max_conc * 1.1))
    
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_cytokine_response(results, patient_id=None, save_path=None):
    """
    Plot IFN-γ levels with activation threshold.

    Parameters:
    -----------
    results : pd.DataFrame or dict
        Simulation results containing time and IFN_gamma columns
    patient_id : str, optional
        Patient ID for title
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ax = plt.subplots(figsize=(12, 8))

    # Extract data
    if isinstance(results, dict):
        time = results['time']
        ifn = results.get('IFN_gamma', results.get('IFN', np.zeros_like(time)))
    else:
        time = results['time'] if 'time' in results.columns else results.index
        ifn = results.get('IFN_gamma', results.get('IFN', np.zeros(len(time))))

    time_weeks = np.array(time) / 7

    # Plot IFN-γ levels
    ax.plot(time_weeks, ifn, color=COLORS['cytokine'], linewidth=3, label='IFN-γ')

    # Add activation threshold (≥100 pg/mL)
    ax.axhline(y=100, color='red', linestyle='--', alpha=0.7,
              label='Activation threshold (100 pg/mL)')

    # Formatting
    ax.set_xlabel('Time (weeks)', fontsize=14, fontweight='bold')
    ax.set_ylabel('IFN-γ (pg/mL)', fontsize=14, fontweight='bold')

    title = 'Cytokine Response (IFN-γ) Over Time'
    if patient_id:
        title += f' - Patient {patient_id}'
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)

    # Set y-axis limits
    max_ifn = np.max(ifn) if len(ifn) > 0 else 200
    ax.set_ylim(0, max(200, max_ifn * 1.1))

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_thyrocyte_depletion(results, patient_id=None, save_path=None):
    """
    Plot thyrocyte mass percentage over time.

    Parameters:
    -----------
    results : pd.DataFrame or dict
        Simulation results containing time and Thyro columns
    patient_id : str, optional
        Patient ID for title
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ax = plt.subplots(figsize=(12, 8))

    # Extract data
    if isinstance(results, dict):
        time = results['time']
        thyro = results.get('Thyro', results.get('thyrocyte_mass', np.ones_like(time)))
    else:
        time = results['time'] if 'time' in results.columns else results.index
        thyro = results.get('Thyro', results.get('thyrocyte_mass', np.ones(len(time))))

    time_weeks = np.array(time) / 7
    thyro_percent = np.array(thyro) * 100  # Convert to percentage

    # Plot thyrocyte mass
    ax.plot(time_weeks, thyro_percent, color=COLORS['thyrocyte'], linewidth=3,
           label='Thyrocyte mass')

    # Add reference lines
    ax.axhline(y=100, color='gray', linestyle='-', alpha=0.5, label='Baseline (100%)')
    ax.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='Mild depletion (80%)')
    ax.axhline(y=50, color='red', linestyle='--', alpha=0.7, label='Severe depletion (50%)')

    # Formatting
    ax.set_xlabel('Time (weeks)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Thyrocyte Mass (%)', fontsize=14, fontweight='bold')

    title = 'Thyrocyte Mass Depletion Over Time'
    if patient_id:
        title += f' - Patient {patient_id}'
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)

    # Set y-axis limits
    min_thyro = np.min(thyro_percent)
    ax.set_ylim(max(0, min_thyro * 0.9), 105)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

# ============================================================================
# POPULATION ANALYSIS PLOTS
# ============================================================================

def plot_incidence_by_drug(population_results, save_path=None, show_ci=True):
    """
    Bar chart comparing hypothyroidism rates across all four drugs.

    Parameters:
    -----------
    population_results : pd.DataFrame
        Population simulation results with drug_type and hypothyroidism columns
    save_path : str, optional
        Path to save the plot
    show_ci : bool
        Whether to show 95% confidence intervals

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Calculate incidence rates by drug
    drug_stats = []
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']

    for drug in drugs:
        drug_data = population_results[population_results['drug_type'] == drug]
        if len(drug_data) > 0:
            any_hypo = drug_data['any_hypothyroidism'].mean() * 100
            grade2_hypo = drug_data['grade2_hypothyroidism'].mean() * 100

            # Calculate 95% CI using Wilson score interval
            n = len(drug_data)
            if show_ci and n > 0:
                # For any hypothyroidism
                p1 = any_hypo / 100
                ci1_low = max(0, (p1 + 1.96**2/(2*n) - 1.96*np.sqrt(p1*(1-p1)/n + 1.96**2/(4*n**2))) / (1 + 1.96**2/n)) * 100
                ci1_high = min(100, (p1 + 1.96**2/(2*n) + 1.96*np.sqrt(p1*(1-p1)/n + 1.96**2/(4*n**2))) / (1 + 1.96**2/n)) * 100

                # For Grade 2+ hypothyroidism
                p2 = grade2_hypo / 100
                ci2_low = max(0, (p2 + 1.96**2/(2*n) - 1.96*np.sqrt(p2*(1-p2)/n + 1.96**2/(4*n**2))) / (1 + 1.96**2/n)) * 100
                ci2_high = min(100, (p2 + 1.96**2/(2*n) + 1.96*np.sqrt(p2*(1-p2)/n + 1.96**2/(4*n**2))) / (1 + 1.96**2/n)) * 100
            else:
                ci1_low = ci1_high = any_hypo
                ci2_low = ci2_high = grade2_hypo

            drug_stats.append({
                'drug': drug.capitalize(),
                'any_hypo': any_hypo,
                'grade2_hypo': grade2_hypo,
                'any_ci_low': ci1_low,
                'any_ci_high': ci1_high,
                'grade2_ci_low': ci2_low,
                'grade2_ci_high': ci2_high,
                'n': n
            })

    df_stats = pd.DataFrame(drug_stats)

    # Plot 1: Any hypothyroidism
    bars1 = ax1.bar(df_stats['drug'], df_stats['any_hypo'],
                    color=COLORS['grade1'], alpha=0.8, label='Any hypothyroidism')

    if show_ci:
        ax1.errorbar(df_stats['drug'], df_stats['any_hypo'],
                    yerr=[df_stats['any_hypo'] - df_stats['any_ci_low'],
                          df_stats['any_ci_high'] - df_stats['any_hypo']],
                    fmt='none', color='black', capsize=5, capthick=2)

    # Add value labels on bars
    for i, bar in enumerate(bars1):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%\n(n={df_stats.iloc[i]["n"]})',
                ha='center', va='bottom', fontweight='bold')

    ax1.set_ylabel('Incidence Rate (%)', fontsize=14, fontweight='bold')
    ax1.set_title('Any Hypothyroidism by Drug Type', fontsize=16, fontweight='bold')
    ax1.set_ylim(0, max(df_stats['any_hypo']) * 1.3)
    ax1.grid(True, alpha=0.3)

    # Plot 2: Grade 2+ hypothyroidism
    bars2 = ax2.bar(df_stats['drug'], df_stats['grade2_hypo'],
                    color=COLORS['grade2'], alpha=0.8, label='Grade 2+ hypothyroidism')

    if show_ci:
        ax2.errorbar(df_stats['drug'], df_stats['grade2_hypo'],
                    yerr=[df_stats['grade2_hypo'] - df_stats['grade2_ci_low'],
                          df_stats['grade2_ci_high'] - df_stats['grade2_hypo']],
                    fmt='none', color='black', capsize=5, capthick=2)

    # Add value labels on bars
    for i, bar in enumerate(bars2):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{height:.1f}%\n(n={df_stats.iloc[i]["n"]})',
                ha='center', va='bottom', fontweight='bold')

    ax2.set_ylabel('Incidence Rate (%)', fontsize=14, fontweight='bold')
    ax2.set_title('Grade 2+ Hypothyroidism by Drug Type', fontsize=16, fontweight='bold')
    ax2.set_ylim(0, max(max(df_stats['grade2_hypo']) * 1.4, 5))
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_onset_distribution(population_results, save_path=None):
    """
    Histogram/density plots of time-to-onset with median markers.

    Parameters:
    -----------
    population_results : pd.DataFrame
        Population simulation results with time_to_onset_days column
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Filter patients who developed hypothyroidism
    hypo_patients = population_results[population_results['any_hypothyroidism'] == True]

    if len(hypo_patients) == 0:
        # No hypothyroidism cases
        ax1.text(0.5, 0.5, 'No hypothyroidism cases\nto display',
                ha='center', va='center', transform=ax1.transAxes, fontsize=14)
        ax2.text(0.5, 0.5, 'No hypothyroidism cases\nto display',
                ha='center', va='center', transform=ax2.transAxes, fontsize=14)
    else:
        # Convert to weeks
        onset_weeks = hypo_patients['time_to_onset_days'] / 7

        # Plot 1: Histogram
        ax1.hist(onset_weeks, bins=20, color=COLORS['grade1'], alpha=0.7,
                edgecolor='black', linewidth=1)

        # Add median line
        median_onset = np.median(onset_weeks)
        ax1.axvline(median_onset, color='red', linestyle='--', linewidth=2,
                   label=f'Median: {median_onset:.1f} weeks')

        ax1.set_xlabel('Time to Onset (weeks)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Number of Patients', fontsize=14, fontweight='bold')
        ax1.set_title('Distribution of Time to Hypothyroidism Onset',
                     fontsize=16, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: Density by drug type
        drugs = hypo_patients['drug_type'].unique()
        for drug in drugs:
            drug_data = hypo_patients[hypo_patients['drug_type'] == drug]
            if len(drug_data) > 0:
                drug_onset = drug_data['time_to_onset_days'] / 7
                ax2.hist(drug_onset, bins=15, alpha=0.6, label=drug.capitalize(),
                        density=True, histtype='step', linewidth=2)

        ax2.set_xlabel('Time to Onset (weeks)', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Density', fontsize=14, fontweight='bold')
        ax2.set_title('Onset Distribution by Drug Type', fontsize=16, fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_risk_stratification(population_results, save_path=None):
    """
    Heatmap showing hypothyroidism rates by risk factors.

    Parameters:
    -----------
    population_results : pd.DataFrame
        Population simulation results with risk factor columns
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Risk factor analysis
    risk_factors = ['age', 'sex', 'HLA_DRB1_03', 'TPO_Ab_positive']

    # Age stratification (>60 vs ≤60)
    if 'age' in population_results.columns:
        age_groups = population_results.copy()
        age_groups['age_group'] = age_groups['age'].apply(lambda x: '>60' if x > 60 else '≤60')
        age_stats = age_groups.groupby('age_group')['any_hypothyroidism'].agg(['mean', 'count']).reset_index()
        age_stats['incidence'] = age_stats['mean'] * 100

        bars = ax1.bar(age_stats['age_group'], age_stats['incidence'],
                      color=[COLORS['grade1'], COLORS['grade2']], alpha=0.8)
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                    f'{height:.1f}%\n(n={age_stats.iloc[i]["count"]})',
                    ha='center', va='bottom', fontweight='bold')

        ax1.set_ylabel('Incidence Rate (%)', fontsize=12, fontweight='bold')
        ax1.set_title('Hypothyroidism by Age Group', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)

    # Sex stratification
    if 'sex' in population_results.columns:
        sex_stats = population_results.groupby('sex')['any_hypothyroidism'].agg(['mean', 'count']).reset_index()
        sex_stats['incidence'] = sex_stats['mean'] * 100

        bars = ax2.bar(sex_stats['sex'], sex_stats['incidence'],
                      color=[COLORS['tsh'], COLORS['t3']], alpha=0.8)
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                    f'{height:.1f}%\n(n={sex_stats.iloc[i]["count"]})',
                    ha='center', va='bottom', fontweight='bold')

        ax2.set_ylabel('Incidence Rate (%)', fontsize=12, fontweight='bold')
        ax2.set_title('Hypothyroidism by Sex', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)

    # HLA-DRB1*03 stratification
    if 'HLA_DRB1_03' in population_results.columns:
        hla_stats = population_results.groupby('HLA_DRB1_03')['any_hypothyroidism'].agg(['mean', 'count']).reset_index()
        hla_stats['incidence'] = hla_stats['mean'] * 100
        hla_stats['HLA_status'] = hla_stats['HLA_DRB1_03'].map({True: 'HLA+', False: 'HLA-'})

        bars = ax3.bar(hla_stats['HLA_status'], hla_stats['incidence'],
                      color=[COLORS['normal'], COLORS['cytokine']], alpha=0.8)
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                    f'{height:.1f}%\n(n={hla_stats.iloc[i]["count"]})',
                    ha='center', va='bottom', fontweight='bold')

        ax3.set_ylabel('Incidence Rate (%)', fontsize=12, fontweight='bold')
        ax3.set_title('Hypothyroidism by HLA-DRB1*03 Status', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)

    # TPO antibody stratification
    if 'TPO_Ab_positive' in population_results.columns:
        tpo_stats = population_results.groupby('TPO_Ab_positive')['any_hypothyroidism'].agg(['mean', 'count']).reset_index()
        tpo_stats['incidence'] = tpo_stats['mean'] * 100
        tpo_stats['TPO_status'] = tpo_stats['TPO_Ab_positive'].map({True: 'TPO+', False: 'TPO-'})

        bars = ax4.bar(tpo_stats['TPO_status'], tpo_stats['incidence'],
                      color=[COLORS['drug'], COLORS['thyrocyte']], alpha=0.8)
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                    f'{height:.1f}%\n(n={tpo_stats.iloc[i]["count"]})',
                    ha='center', va='bottom', fontweight='bold')

        ax4.set_ylabel('Incidence Rate (%)', fontsize=12, fontweight='bold')
        ax4.set_title('Hypothyroidism by TPO Antibody Status', fontsize=14, fontweight='bold')
        ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_dose_response(population_results, save_path=None):
    """
    Scatter plots showing drug concentration vs. hypothyroidism severity.

    Parameters:
    -----------
    population_results : pd.DataFrame
        Population simulation results with drug concentration and TSH data
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Get drug concentration data (if available)
    if 'peak_drug_concentration' in population_results.columns:
        drug_conc = population_results['peak_drug_concentration'] / 1000  # Convert to μg/mL
    else:
        # Create synthetic drug concentration data based on drug type
        drug_conc = []
        for _, row in population_results.iterrows():
            if row['drug_type'] == 'nivolumab':
                conc = np.random.normal(80, 20)
            elif row['drug_type'] == 'pembrolizumab':
                conc = np.random.normal(75, 18)
            elif row['drug_type'] == 'atezolizumab':
                conc = np.random.normal(85, 22)
            else:  # durvalumab
                conc = np.random.normal(70, 15)
            drug_conc.append(max(0, conc))
        drug_conc = np.array(drug_conc)

    peak_tsh = population_results['peak_TSH_mIU_per_L']

    # Plot 1: Drug concentration vs Peak TSH
    colors = [COLORS['grade2'] if hypo else COLORS['normal']
              for hypo in population_results['any_hypothyroidism']]

    ax1.scatter(drug_conc, peak_tsh, c=colors, alpha=0.6, s=50)
    ax1.axhline(y=4.5, color=COLORS['grade1'], linestyle='--', alpha=0.7)
    ax1.axhline(y=10.0, color=COLORS['grade2'], linestyle='--', alpha=0.7)
    ax1.axvline(x=50, color='red', linestyle='--', alpha=0.7)

    ax1.set_xlabel('Peak Drug Concentration (μg/mL)', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Peak TSH (mIU/L)', fontsize=12, fontweight='bold')
    ax1.set_title('Drug Concentration vs TSH Response', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)

    # Add legend
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor=COLORS['normal'], label='No hypothyroidism'),
                      Patch(facecolor=COLORS['grade2'], label='Hypothyroidism')]
    ax1.legend(handles=legend_elements, loc='upper left')

    # Plot 2: Drug concentration by drug type
    drugs = population_results['drug_type'].unique()
    drug_positions = np.arange(len(drugs))

    for i, drug in enumerate(drugs):
        drug_data = population_results[population_results['drug_type'] == drug]
        drug_conc_subset = drug_conc[population_results['drug_type'] == drug]

        # Box plot
        bp = ax2.boxplot(drug_conc_subset, positions=[i], widths=0.6, patch_artist=True)
        bp['boxes'][0].set_facecolor(COLORS['drug'])
        bp['boxes'][0].set_alpha(0.7)

    ax2.set_xticks(drug_positions)
    ax2.set_xticklabels([drug.capitalize() for drug in drugs], rotation=45)
    ax2.set_ylabel('Drug Concentration (μg/mL)', fontsize=12, fontweight='bold')
    ax2.set_title('Drug Concentration Distribution by Type', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)

    # Plot 3: TSH vs Time to onset
    hypo_patients = population_results[population_results['any_hypothyroidism'] == True]
    if len(hypo_patients) > 0:
        onset_weeks = hypo_patients['time_to_onset_days'] / 7
        hypo_tsh = hypo_patients['peak_TSH_mIU_per_L']

        ax3.scatter(onset_weeks, hypo_tsh, c=COLORS['grade1'], alpha=0.6, s=50)
        ax3.set_xlabel('Time to Onset (weeks)', fontsize=12, fontweight='bold')
        ax3.set_ylabel('Peak TSH (mIU/L)', fontsize=12, fontweight='bold')
        ax3.set_title('TSH vs Time to Onset', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
    else:
        ax3.text(0.5, 0.5, 'No hypothyroidism\ncases to display',
                ha='center', va='center', transform=ax3.transAxes, fontsize=12)

    # Plot 4: Grade distribution
    grade_counts = population_results.groupby('drug_type').agg({
        'any_hypothyroidism': 'sum',
        'grade2_hypothyroidism': 'sum'
    }).reset_index()

    grade_counts['grade1_only'] = grade_counts['any_hypothyroidism'] - grade_counts['grade2_hypothyroidism']

    x_pos = np.arange(len(grade_counts))
    width = 0.35

    ax4.bar(x_pos, grade_counts['grade1_only'], width, label='Grade 1',
           color=COLORS['grade1'], alpha=0.8)
    ax4.bar(x_pos, grade_counts['grade2_hypothyroidism'], width,
           bottom=grade_counts['grade1_only'], label='Grade 2+',
           color=COLORS['grade2'], alpha=0.8)

    ax4.set_xlabel('Drug Type', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Number of Cases', fontsize=12, fontweight='bold')
    ax4.set_title('Hypothyroidism Grade Distribution', fontsize=14, fontweight='bold')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels([drug.capitalize() for drug in grade_counts['drug_type']], rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

# ============================================================================
# VALIDATION PLOTS
# ============================================================================

def plot_literature_comparison(model_results, literature_data=None, save_path=None):
    """
    Side-by-side comparison of model predictions vs. published incidence rates.

    Parameters:
    -----------
    model_results : pd.DataFrame
        Model simulation results
    literature_data : dict, optional
        Literature incidence rates by drug
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    # Default literature data if not provided
    if literature_data is None:
        literature_data = {
            'nivolumab': {'any': 8.0, 'grade2': 3.5, 'source': 'Barroso-Sousa et al. 2018'},
            'pembrolizumab': {'any': 12.0, 'grade2': 5.2, 'source': 'de Filette et al. 2019'},
            'atezolizumab': {'any': 5.5, 'grade2': 2.1, 'source': 'Muir et al. 2020'},
            'durvalumab': {'any': 5.0, 'grade2': 1.8, 'source': 'Frelaut et al. 2021'}
        }

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Calculate model incidence rates
    model_stats = []
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']

    for drug in drugs:
        drug_data = model_results[model_results['drug_type'] == drug]
        if len(drug_data) > 0:
            any_hypo = drug_data['any_hypothyroidism'].mean() * 100
            grade2_hypo = drug_data['grade2_hypothyroidism'].mean() * 100
        else:
            any_hypo = 0
            grade2_hypo = 0

        model_stats.append({
            'drug': drug.capitalize(),
            'model_any': any_hypo,
            'model_grade2': grade2_hypo,
            'lit_any': literature_data.get(drug, {}).get('any', 0),
            'lit_grade2': literature_data.get(drug, {}).get('grade2', 0)
        })

    df_comparison = pd.DataFrame(model_stats)

    # Plot 1: Any hypothyroidism comparison
    x_pos = np.arange(len(df_comparison))
    width = 0.35

    bars1 = ax1.bar(x_pos - width/2, df_comparison['model_any'], width,
                   label='Model', color=COLORS['tsh'], alpha=0.8)
    bars2 = ax1.bar(x_pos + width/2, df_comparison['lit_any'], width,
                   label='Literature', color=COLORS['grade1'], alpha=0.8)

    # Add value labels
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        ax1.text(bar1.get_x() + bar1.get_width()/2., bar1.get_height() + 0.2,
                f'{df_comparison.iloc[i]["model_any"]:.1f}%',
                ha='center', va='bottom', fontsize=10, fontweight='bold')
        ax1.text(bar2.get_x() + bar2.get_width()/2., bar2.get_height() + 0.2,
                f'{df_comparison.iloc[i]["lit_any"]:.1f}%',
                ha='center', va='bottom', fontsize=10, fontweight='bold')

    ax1.set_xlabel('Drug Type', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Any Hypothyroidism (%)', fontsize=14, fontweight='bold')
    ax1.set_title('Model vs Literature: Any Hypothyroidism', fontsize=16, fontweight='bold')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(df_comparison['drug'])
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: Grade 2+ hypothyroidism comparison
    bars3 = ax2.bar(x_pos - width/2, df_comparison['model_grade2'], width,
                   label='Model', color=COLORS['t3'], alpha=0.8)
    bars4 = ax2.bar(x_pos + width/2, df_comparison['lit_grade2'], width,
                   label='Literature', color=COLORS['grade2'], alpha=0.8)

    # Add value labels
    for i, (bar3, bar4) in enumerate(zip(bars3, bars4)):
        ax2.text(bar3.get_x() + bar3.get_width()/2., bar3.get_height() + 0.1,
                f'{df_comparison.iloc[i]["model_grade2"]:.1f}%',
                ha='center', va='bottom', fontsize=10, fontweight='bold')
        ax2.text(bar4.get_x() + bar4.get_width()/2., bar4.get_height() + 0.1,
                f'{df_comparison.iloc[i]["lit_grade2"]:.1f}%',
                ha='center', va='bottom', fontsize=10, fontweight='bold')

    ax2.set_xlabel('Drug Type', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Grade 2+ Hypothyroidism (%)', fontsize=14, fontweight='bold')
    ax2.set_title('Model vs Literature: Grade 2+ Hypothyroidism', fontsize=16, fontweight='bold')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(df_comparison['drug'])
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_ctcae_validation(population_results, save_path=None):
    """
    Validation plots showing CTCAE grade classification accuracy.

    Parameters:
    -----------
    population_results : pd.DataFrame
        Population simulation results with TSH and T3 data
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Plot 1: TSH distribution by grade
    normal_tsh = population_results[population_results['any_hypothyroidism'] == False]['peak_TSH_mIU_per_L']
    grade1_tsh = population_results[
        (population_results['any_hypothyroidism'] == True) &
        (population_results['grade2_hypothyroidism'] == False)
    ]['peak_TSH_mIU_per_L']
    grade2_tsh = population_results[population_results['grade2_hypothyroidism'] == True]['peak_TSH_mIU_per_L']

    ax1.hist(normal_tsh, bins=20, alpha=0.7, label='Normal', color=COLORS['normal'], density=True)
    if len(grade1_tsh) > 0:
        ax1.hist(grade1_tsh, bins=20, alpha=0.7, label='Grade 1', color=COLORS['grade1'], density=True)
    if len(grade2_tsh) > 0:
        ax1.hist(grade2_tsh, bins=20, alpha=0.7, label='Grade 2+', color=COLORS['grade2'], density=True)

    ax1.axvline(x=4.5, color='black', linestyle='--', alpha=0.7)
    ax1.axvline(x=10.0, color='black', linestyle='--', alpha=0.7)
    ax1.set_xlabel('Peak TSH (mIU/L)', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Density', fontsize=12, fontweight='bold')
    ax1.set_title('TSH Distribution by CTCAE Grade', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: T3 distribution by grade
    normal_t3 = population_results[population_results['any_hypothyroidism'] == False]['min_T3_pmol_per_L']
    hypo_t3 = population_results[population_results['any_hypothyroidism'] == True]['min_T3_pmol_per_L']

    ax2.hist(normal_t3, bins=20, alpha=0.7, label='Normal', color=COLORS['normal'], density=True)
    if len(hypo_t3) > 0:
        ax2.hist(hypo_t3, bins=20, alpha=0.7, label='Hypothyroidism', color=COLORS['grade1'], density=True)

    ax2.axvline(x=3.1, color='black', linestyle='--', alpha=0.7, label='T3 threshold')
    ax2.set_xlabel('Minimum T3 (pmol/L)', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Density', fontsize=12, fontweight='bold')
    ax2.set_title('T3 Distribution by Hypothyroidism Status', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Plot 3: Grade classification matrix
    # Create confusion matrix-like visualization
    total_patients = len(population_results)
    normal_count = len(population_results[population_results['any_hypothyroidism'] == False])
    grade1_count = len(population_results[
        (population_results['any_hypothyroidism'] == True) &
        (population_results['grade2_hypothyroidism'] == False)
    ])
    grade2_count = len(population_results[population_results['grade2_hypothyroidism'] == True])

    categories = ['Normal', 'Grade 1', 'Grade 2+']
    counts = [normal_count, grade1_count, grade2_count]
    percentages = [c/total_patients*100 for c in counts]
    colors = [COLORS['normal'], COLORS['grade1'], COLORS['grade2']]

    bars = ax3.bar(categories, percentages, color=colors, alpha=0.8)

    # Add count labels
    for bar, count, pct in zip(bars, counts, percentages):
        ax3.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                f'{count}\n({pct:.1f}%)',
                ha='center', va='bottom', fontsize=11, fontweight='bold')

    ax3.set_ylabel('Percentage of Patients (%)', fontsize=12, fontweight='bold')
    ax3.set_title('CTCAE Grade Distribution', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)

    # Plot 4: Time to grade progression
    if len(population_results[population_results['any_hypothyroidism'] == True]) > 0:
        onset_data = population_results[population_results['any_hypothyroidism'] == True]
        # Filter out NaN values for time to onset
        onset_data = onset_data[~onset_data['time_to_onset_days'].isna()]

        if len(onset_data) > 0:
            onset_weeks = onset_data['time_to_onset_days'] / 7

            # Separate by grade
            grade1_onset = onset_data[onset_data['grade2_hypothyroidism'] == False]['time_to_onset_days'] / 7
            grade2_onset = onset_data[onset_data['grade2_hypothyroidism'] == True]['time_to_onset_days'] / 7

            # Filter out any remaining NaN values
            grade1_onset = grade1_onset[~grade1_onset.isna()]
            grade2_onset = grade2_onset[~grade2_onset.isna()]

            if len(grade1_onset) > 0:
                ax4.hist(grade1_onset, bins=15, alpha=0.7, label='Grade 1', color=COLORS['grade1'])
            if len(grade2_onset) > 0:
                ax4.hist(grade2_onset, bins=15, alpha=0.7, label='Grade 2+', color=COLORS['grade2'])

            ax4.set_xlabel('Time to Onset (weeks)', fontsize=12, fontweight='bold')
            ax4.set_ylabel('Number of Patients', fontsize=12, fontweight='bold')
            ax4.set_title('Time to Onset by Grade', fontsize=14, fontweight='bold')
            if len(grade1_onset) > 0 or len(grade2_onset) > 0:
                ax4.legend()
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, 'No valid onset times\nfor analysis',
                    ha='center', va='center', transform=ax4.transAxes, fontsize=12)
    else:
        ax4.text(0.5, 0.5, 'No hypothyroidism\ncases to display',
                ha='center', va='center', transform=ax4.transAxes, fontsize=12)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_sensitivity_analysis(sensitivity_results, save_path=None):
    """
    Tornado plots showing parameter sensitivity on hypothyroidism incidence.

    Parameters:
    -----------
    sensitivity_results : pd.DataFrame
        Results from sensitivity analysis with parameter variations
    save_path : str, optional
        Path to save the plot

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Default sensitivity data if not provided
    if sensitivity_results is None or len(sensitivity_results) == 0:
        # Create example sensitivity data
        parameters = ['k_death', 'EC50_IFN_death', 'k_IFN_prod', 'k_IFN_clear', 'susceptibility_rate']
        baseline_incidence = 8.0

        # Simulate parameter variations (±50%)
        low_values = [6.2, 5.8, 7.5, 8.3, 6.8]  # Lower incidence when parameter decreased
        high_values = [9.8, 10.2, 8.5, 7.7, 9.2]  # Higher incidence when parameter increased

        sensitivity_data = pd.DataFrame({
            'parameter': parameters,
            'baseline': [baseline_incidence] * len(parameters),
            'low_variation': low_values,
            'high_variation': high_values
        })
    else:
        sensitivity_data = sensitivity_results

    # Calculate sensitivity indices
    sensitivity_data['sensitivity'] = (
        sensitivity_data['high_variation'] - sensitivity_data['low_variation']
    ) / 2
    sensitivity_data = sensitivity_data.sort_values('sensitivity', ascending=True)

    # Plot 1: Tornado plot for any hypothyroidism
    y_pos = np.arange(len(sensitivity_data))

    # Calculate bar lengths
    left_bars = sensitivity_data['baseline'] - sensitivity_data['low_variation']
    right_bars = sensitivity_data['high_variation'] - sensitivity_data['baseline']

    # Create horizontal bars
    ax1.barh(y_pos, -left_bars, left=sensitivity_data['baseline'],
            color=COLORS['tsh'], alpha=0.7, label='Decreased parameter')
    ax1.barh(y_pos, right_bars, left=sensitivity_data['baseline'],
            color=COLORS['grade2'], alpha=0.7, label='Increased parameter')

    # Add baseline line
    ax1.axvline(x=sensitivity_data['baseline'].iloc[0], color='black',
               linestyle='-', linewidth=2, label='Baseline')

    ax1.set_yticks(y_pos)
    ax1.set_yticklabels(sensitivity_data['parameter'])
    ax1.set_xlabel('Hypothyroidism Incidence (%)', fontsize=14, fontweight='bold')
    ax1.set_title('Parameter Sensitivity Analysis', fontsize=16, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: Sensitivity index ranking
    ax2.barh(y_pos, sensitivity_data['sensitivity'], color=COLORS['cytokine'], alpha=0.8)

    # Add value labels
    for i, (idx, row) in enumerate(sensitivity_data.iterrows()):
        ax2.text(row['sensitivity'] + 0.05, i, f'{row["sensitivity"]:.2f}',
                va='center', fontweight='bold')

    ax2.set_yticks(y_pos)
    ax2.set_yticklabels(sensitivity_data['parameter'])
    ax2.set_xlabel('Sensitivity Index', fontsize=14, fontweight='bold')
    ax2.set_title('Parameter Sensitivity Ranking', fontsize=16, fontweight='bold')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def save_all_plots(results, population_results=None, output_dir='plots',
                  patient_id=None, drug_type='nivolumab'):
    """
    Generate and save all available plots for a complete analysis.

    Parameters:
    -----------
    results : dict or pd.DataFrame
        Single patient simulation results
    population_results : pd.DataFrame, optional
        Population simulation results
    output_dir : str
        Directory to save plots
    patient_id : str, optional
        Patient ID for single patient plots
    drug_type : str
        Type of checkpoint inhibitor drug

    Returns:
    --------
    dict : Dictionary of generated plot file paths
    """
    import os

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    plot_files = {}

    # Single patient plots
    if results is not None:
        plot_files['tsh_timecourse'] = os.path.join(output_dir, 'tsh_timecourse.png')
        plot_tsh_timecourse(results, patient_id, plot_files['tsh_timecourse'])

        plot_files['t3_timecourse'] = os.path.join(output_dir, 't3_timecourse.png')
        plot_t3_timecourse(results, patient_id, plot_files['t3_timecourse'])

        plot_files['drug_pk'] = os.path.join(output_dir, 'drug_pk_profile.png')
        plot_drug_pk_profile(results, drug_type, patient_id, plot_files['drug_pk'])

        plot_files['cytokine'] = os.path.join(output_dir, 'cytokine_response.png')
        plot_cytokine_response(results, patient_id, plot_files['cytokine'])

        plot_files['thyrocyte'] = os.path.join(output_dir, 'thyrocyte_depletion.png')
        plot_thyrocyte_depletion(results, patient_id, plot_files['thyrocyte'])

    # Population plots
    if population_results is not None:
        plot_files['incidence'] = os.path.join(output_dir, 'incidence_by_drug.png')
        plot_incidence_by_drug(population_results, plot_files['incidence'])

        plot_files['onset'] = os.path.join(output_dir, 'onset_distribution.png')
        plot_onset_distribution(population_results, plot_files['onset'])

        plot_files['risk'] = os.path.join(output_dir, 'risk_stratification.png')
        plot_risk_stratification(population_results, plot_files['risk'])

        plot_files['dose_response'] = os.path.join(output_dir, 'dose_response.png')
        plot_dose_response(population_results, plot_files['dose_response'])

        plot_files['literature'] = os.path.join(output_dir, 'literature_comparison.png')
        plot_literature_comparison(population_results, save_path=plot_files['literature'])

        plot_files['ctcae'] = os.path.join(output_dir, 'ctcae_validation.png')
        plot_ctcae_validation(population_results, plot_files['ctcae'])

        plot_files['sensitivity'] = os.path.join(output_dir, 'sensitivity_analysis.png')
        plot_sensitivity_analysis(None, plot_files['sensitivity'])  # Use default data

    return plot_files

def create_summary_dashboard(results, population_results, save_path='dashboard.png'):
    """
    Create a comprehensive dashboard with key plots.

    Parameters:
    -----------
    results : dict or pd.DataFrame
        Single patient simulation results
    population_results : pd.DataFrame
        Population simulation results
    save_path : str
        Path to save the dashboard

    Returns:
    --------
    matplotlib.figure.Figure
    """
    fig = plt.figure(figsize=(20, 16))

    # Create a 3x3 grid
    gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

    # Single patient plots (top row)
    ax1 = fig.add_subplot(gs[0, 0])
    ax2 = fig.add_subplot(gs[0, 1])
    ax3 = fig.add_subplot(gs[0, 2])

    # Population plots (middle and bottom rows)
    ax4 = fig.add_subplot(gs[1, 0])
    ax5 = fig.add_subplot(gs[1, 1])
    ax6 = fig.add_subplot(gs[1, 2])
    ax7 = fig.add_subplot(gs[2, 0])
    ax8 = fig.add_subplot(gs[2, 1])
    ax9 = fig.add_subplot(gs[2, 2])

    # Generate individual plots and extract their content
    # This is a simplified version - in practice, you'd need to modify
    # the plotting functions to accept specific axes

    fig.suptitle('QSP Thyroid Model - Comprehensive Analysis Dashboard',
                fontsize=24, fontweight='bold', y=0.98)

    plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig
