#' QSP Thyroid Model - Comprehensive Plotting Functions
#'
#' This module provides publication-quality plotting functions for the QSP thyroid model
#' with calibrated parameters (k_death=0.030, EC50_IFN_death=100) that produce realistic
#' hypothyroidism incidence rates (0-15%).
#'
#' <AUTHOR> Modeling Team

# Load required libraries
suppressMessages({
  library(ggplot2)
  library(dplyr)
  library(gridExtra)
  library(scales)
  library(viridis)
})

# Define consistent color palette (matching Python version)
COLORS <- list(
  tsh = "#2E86AB",      # Blue for TSH
  t3 = "#A23B72",       # Purple for T3
  drug = "#F18F01",     # Orange for drug concentrations
  cytokine = "#C73E1D", # Red for cytokines
  thyrocyte = "#4CAF50", # Green for thyrocytes
  grade1 = "#FFC107",   # Amber for Grade 1
  grade2 = "#FF5722",   # Deep orange for Grade 2+
  normal = "#4CAF50"    # Green for normal
)

# Set ggplot2 theme for publication quality
theme_qsp <- function() {
  theme_minimal() +
    theme(
      text = element_text(family = "Arial", size = 12),
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5, margin = margin(b = 20)),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 10),
      panel.grid.minor = element_blank(),
      panel.grid.major = element_line(colour = "grey90"),
      strip.text = element_text(size = 12, face = "bold")
    )
}

#' Plot TSH progression with CTCAE grade thresholds
#'
#' @param results Data frame or list containing time and TSH columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @param show_grades Whether to show CTCAE grade threshold lines
#' @return ggplot object
#' @export
plot_tsh_timecourse <- function(results, patient_id = NULL, save_path = NULL, show_grades = TRUE) {
  
  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    df <- data.frame(
      time = results$time,
      TSH = results$TSH
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
  }
  
  # Convert time to weeks
  df$time_weeks <- df$time / 7
  
  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = TSH)) +
    geom_line(color = COLORS$tsh, linewidth = 1.5) +
    labs(
      x = "Time (weeks)",
      y = "TSH (mIU/L)",
      title = if (is.null(patient_id)) "TSH Progression Over Time" else paste("TSH Progression Over Time - Patient", patient_id)
    ) +
    theme_qsp()
  
  if (show_grades) {
    # Add CTCAE grade threshold lines
    p <- p +
      geom_hline(yintercept = 4.5, color = COLORS$grade1, linetype = "dashed", alpha = 0.7, linewidth = 1) +
      geom_hline(yintercept = 10.0, color = COLORS$grade2, linetype = "dashed", alpha = 0.7, linewidth = 1) +
      annotate("rect", xmin = -Inf, xmax = Inf, ymin = 0.4, ymax = 4.5, 
               alpha = 0.1, fill = COLORS$normal) +
      annotate("text", x = max(df$time_weeks) * 0.8, y = 4.5, 
               label = "Grade 1 threshold (4.5 mIU/L)", vjust = -0.5, size = 3) +
      annotate("text", x = max(df$time_weeks) * 0.8, y = 10.0, 
               label = "Grade 2 threshold (10.0 mIU/L)", vjust = -0.5, size = 3)
  }
  
  # Set reasonable y-axis limits
  max_tsh <- max(df$TSH, na.rm = TRUE)
  p <- p + ylim(0, max(12, max_tsh * 1.1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot T3 levels with normal range and hypothyroidism threshold
#'
#' @param results Data frame or list containing time and T3 columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @param show_normal_range Whether to show normal T3 range
#' @return ggplot object
#' @export
plot_t3_timecourse <- function(results, patient_id = NULL, save_path = NULL, show_normal_range = TRUE) {
  
  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    df <- data.frame(
      time = results$time,
      T3 = results$T3
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
  }
  
  # Convert time to weeks
  df$time_weeks <- df$time / 7
  
  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = T3)) +
    geom_line(color = COLORS$t3, linewidth = 1.5) +
    labs(
      x = "Time (weeks)",
      y = "T3 (pmol/L)",
      title = if (is.null(patient_id)) "T3 Levels Over Time" else paste("T3 Levels Over Time - Patient", patient_id)
    ) +
    theme_qsp()
  
  if (show_normal_range) {
    # Normal T3 range: 3.1-6.8 pmol/L
    p <- p +
      annotate("rect", xmin = -Inf, xmax = Inf, ymin = 3.1, ymax = 6.8, 
               alpha = 0.1, fill = COLORS$normal) +
      geom_hline(yintercept = 3.1, color = COLORS$grade1, linetype = "dashed", alpha = 0.7, linewidth = 1) +
      annotate("text", x = max(df$time_weeks) * 0.8, y = 3.1, 
               label = "Hypothyroidism threshold (<3.1 pmol/L)", vjust = 1.2, size = 3) +
      annotate("text", x = max(df$time_weeks) * 0.2, y = 5.0, 
               label = "Normal range (3.1-6.8 pmol/L)", size = 3)
  }
  
  # Set reasonable y-axis limits
  min_t3 <- min(df$T3, na.rm = TRUE)
  max_t3 <- max(df$T3, na.rm = TRUE)
  p <- p + ylim(max(0, min_t3 * 0.9), max_t3 * 1.1)
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot drug concentration over treatment cycles with therapeutic thresholds
#'
#' @param results Data frame or list containing time and drug concentration
#' @param drug_type Type of checkpoint inhibitor drug
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_drug_pk_profile <- function(results, drug_type = "nivolumab", patient_id = NULL, save_path = NULL) {
  
  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    drug_conc <- if ("drug_concentration" %in% names(results)) {
      results$drug_concentration
    } else if ("C_drug" %in% names(results)) {
      results$C_drug
    } else {
      rep(0, length(results$time))
    }
    
    df <- data.frame(
      time = results$time,
      drug_concentration = drug_conc
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
    if (!"drug_concentration" %in% names(df)) {
      if ("C_drug" %in% names(df)) {
        df$drug_concentration <- df$C_drug
      } else {
        df$drug_concentration <- 0
      }
    }
  }
  
  # Convert time to weeks and concentration to μg/mL
  df$time_weeks <- df$time / 7
  df$drug_conc_ug <- df$drug_concentration / 1000  # ng/mL to μg/mL
  
  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = drug_conc_ug)) +
    geom_line(color = COLORS$drug, linewidth = 1.5) +
    geom_hline(yintercept = 50, color = "red", linetype = "dashed", alpha = 0.7, linewidth = 1) +
    labs(
      x = "Time (weeks)",
      y = "Drug Concentration (μg/mL)",
      title = if (is.null(patient_id)) {
        paste(tools::toTitleCase(drug_type), "Pharmacokinetic Profile")
      } else {
        paste(tools::toTitleCase(drug_type), "Pharmacokinetic Profile - Patient", patient_id)
      }
    ) +
    theme_qsp()
  
  # Add dosing schedule markers (every 2 weeks)
  max_weeks <- max(df$time_weeks, na.rm = TRUE)
  dosing_weeks <- seq(0, max_weeks, by = 2)
  for (week in dosing_weeks) {
    p <- p + geom_vline(xintercept = week, color = "gray", linetype = "dotted", alpha = 0.5)
  }
  
  # Add threshold annotation
  p <- p + annotate("text", x = max_weeks * 0.7, y = 55, 
                   label = "Activation threshold (50 μg/mL)", size = 3)
  
  # Set y-axis limits
  max_conc <- max(df$drug_conc_ug, na.rm = TRUE)
  p <- p + ylim(0, max(100, max_conc * 1.1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }

  return(p)
}

#' Plot IFN-γ levels with activation threshold
#'
#' @param results Data frame or list containing time and IFN_gamma columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_cytokine_response <- function(results, patient_id = NULL, save_path = NULL) {

  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    ifn <- if ("IFN_gamma" %in% names(results)) {
      results$IFN_gamma
    } else if ("IFN" %in% names(results)) {
      results$IFN
    } else {
      rep(0, length(results$time))
    }

    df <- data.frame(
      time = results$time,
      IFN_gamma = ifn
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
    if (!"IFN_gamma" %in% names(df)) {
      if ("IFN" %in% names(df)) {
        df$IFN_gamma <- df$IFN
      } else {
        df$IFN_gamma <- 0
      }
    }
  }

  # Convert time to weeks
  df$time_weeks <- df$time / 7

  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = IFN_gamma)) +
    geom_line(color = COLORS$cytokine, linewidth = 1.5) +
    geom_hline(yintercept = 100, color = "red", linetype = "dashed", alpha = 0.7, linewidth = 1) +
    labs(
      x = "Time (weeks)",
      y = "IFN-γ (pg/mL)",
      title = if (is.null(patient_id)) "Cytokine Response (IFN-γ) Over Time" else paste("Cytokine Response (IFN-γ) Over Time - Patient", patient_id)
    ) +
    theme_qsp()

  # Add threshold annotation
  max_weeks <- max(df$time_weeks, na.rm = TRUE)
  p <- p + annotate("text", x = max_weeks * 0.7, y = 110,
                   label = "Activation threshold (100 pg/mL)", size = 3)

  # Set y-axis limits
  max_ifn <- max(df$IFN_gamma, na.rm = TRUE)
  p <- p + ylim(0, max(200, max_ifn * 1.1))

  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }

  return(p)
}

#' Plot thyrocyte mass percentage over time
#'
#' @param results Data frame or list containing time and Thyro columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_thyrocyte_depletion <- function(results, patient_id = NULL, save_path = NULL) {

  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    thyro <- if ("Thyro" %in% names(results)) {
      results$Thyro
    } else if ("thyrocyte_mass" %in% names(results)) {
      results$thyrocyte_mass
    } else {
      rep(1, length(results$time))
    }

    df <- data.frame(
      time = results$time,
      Thyro = thyro
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
    if (!"Thyro" %in% names(df)) {
      if ("thyrocyte_mass" %in% names(df)) {
        df$Thyro <- df$thyrocyte_mass
      } else {
        df$Thyro <- 1
      }
    }
  }

  # Convert time to weeks and thyrocyte mass to percentage
  df$time_weeks <- df$time / 7
  df$thyro_percent <- df$Thyro * 100

  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = thyro_percent)) +
    geom_line(color = COLORS$thyrocyte, linewidth = 1.5) +
    geom_hline(yintercept = 100, color = "gray", linetype = "solid", alpha = 0.5, size = 1) +
    geom_hline(yintercept = 80, color = "orange", linetype = "dashed", alpha = 0.7, size = 1) +
    geom_hline(yintercept = 50, color = "red", linetype = "dashed", alpha = 0.7, size = 1) +
    labs(
      x = "Time (weeks)",
      y = "Thyrocyte Mass (%)",
      title = if (is.null(patient_id)) "Thyrocyte Mass Depletion Over Time" else paste("Thyrocyte Mass Depletion Over Time - Patient", patient_id)
    ) +
    theme_qsp()

  # Add reference line annotations
  max_weeks <- max(df$time_weeks, na.rm = TRUE)
  p <- p +
    annotate("text", x = max_weeks * 0.8, y = 100, label = "Baseline (100%)", vjust = -0.5, size = 3) +
    annotate("text", x = max_weeks * 0.8, y = 80, label = "Mild depletion (80%)", vjust = -0.5, size = 3) +
    annotate("text", x = max_weeks * 0.8, y = 50, label = "Severe depletion (50%)", vjust = -0.5, size = 3)

  # Set y-axis limits
  min_thyro <- min(df$thyro_percent, na.rm = TRUE)
  p <- p + ylim(max(0, min_thyro * 0.9), 105)

  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }

  return(p)
}
