#' @title Test Population Analysis Components
#' @description Unit tests for population analysis and risk stratification

test_that("PopulationParameters class works correctly", {
  # Test default initialization
  pop_params <- PopulationParameters$new()
  
  expect_s3_class(pop_params, "PopulationParameters")
  expect_s3_class(pop_params, "R6")
  
  # Check default values
  expect_equal(pop_params$age_mean, 65.0)
  expect_equal(pop_params$age_std, 10.0)
  expect_equal(pop_params$female_proportion, 0.41)
  expect_equal(pop_params$HLA_DRB1_03_prevalence, 0.15)
  expect_equal(pop_params$TPO_Ab_positive_rate, 0.16)
  
  # Check drug distribution
  expect_true(is.list(pop_params$drug_distribution))
  expect_true(all(names(pop_params$drug_distribution) %in% 
                 c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')))
  expect_equal(sum(unlist(pop_params$drug_distribution)), 1.0, tolerance = 1e-6)
  
  # Test custom initialization
  custom_params <- PopulationParameters$new(
    age_mean = 70.0,
    female_proportion = 0.5
  )
  
  expect_equal(custom_params$age_mean, 70.0)
  expect_equal(custom_params$female_proportion, 0.5)
  expect_equal(custom_params$age_std, 10.0)  # Should keep default
})

test_that("VirtualCohort class initialization works correctly", {
  # Test default initialization
  cohort <- VirtualCohort$new(n_patients = 100, random_state = 123)
  
  expect_s3_class(cohort, "VirtualCohort")
  expect_s3_class(cohort, "R6")
  expect_equal(cohort$n_patients, 100)
  expect_equal(cohort$random_state, 123)
  
  # Check patients data frame
  expect_true(is.data.frame(cohort$patients))
  expect_equal(nrow(cohort$patients), 100)
  
  # Check required columns
  required_cols <- c('patient_id', 'age', 'sex', 'HLA_DRB1_03', 'baseline_TSH',
                    'TPO_Ab_positive', 'TPO_Ab_titer', 'thyroid_volume', 'drug_type',
                    'sex_factor', 'age_factor', 'HLA_factor', 'TPO_Ab_log_titer', 'Thyro_max')
  expect_true(all(required_cols %in% names(cohort$patients)))
  
  # Check value ranges
  expect_true(all(cohort$patients$age >= 18 & cohort$patients$age <= 90))
  expect_true(all(cohort$patients$sex %in% c('M', 'F')))
  expect_true(all(cohort$patients$HLA_DRB1_03 %in% c(0, 1)))
  expect_true(all(cohort$patients$baseline_TSH > 0))
  expect_true(all(cohort$patients$TPO_Ab_positive %in% c(0, 1)))
  expect_true(all(cohort$patients$thyroid_volume > 0))
  expect_true(all(cohort$patients$drug_type %in% 
                 c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')))
})

test_that("VirtualCohort patient generation is realistic", {
  set.seed(42)
  pop_params <- PopulationParameters$new()
  cohort <- VirtualCohort$new(n_patients = 1000, pop_params = pop_params, random_state = 42)
  
  patients <- cohort$patients
  
  # Check age distribution
  age_mean <- mean(patients$age)
  age_sd <- sd(patients$age)
  expect_equal(age_mean, pop_params$age_mean, tolerance = 2.0)  # Within 2 years
  expect_equal(age_sd, pop_params$age_std, tolerance = 2.0)    # Within 2 years
  
  # Check sex distribution
  female_prop <- mean(patients$sex == 'F')
  expect_equal(female_prop, pop_params$female_proportion, tolerance = 0.05)
  
  # Check HLA prevalence
  hla_prev <- mean(patients$HLA_DRB1_03 == 1)
  expect_equal(hla_prev, pop_params$HLA_DRB1_03_prevalence, tolerance = 0.03)
  
  # Check TPO antibody prevalence
  tpo_prev <- mean(patients$TPO_Ab_positive == 1)
  expect_equal(tpo_prev, pop_params$TPO_Ab_positive_rate, tolerance = 0.03)
  
  # Check drug distribution
  drug_counts <- table(patients$drug_type)
  drug_props <- drug_counts / sum(drug_counts)
  
  for (drug in names(pop_params$drug_distribution)) {
    expected_prop <- pop_params$drug_distribution[[drug]]
    actual_prop <- drug_props[drug]
    expect_equal(actual_prop, expected_prop, tolerance = 0.05)
  }
  
  # Check covariate effects
  expect_true(all(patients$sex_factor[patients$sex == 'M'] == 1.0))
  expect_true(all(patients$sex_factor[patients$sex == 'F'] == 1.3))
  expect_true(all(patients$age_factor[patients$age <= 60] == 1.0))
  expect_true(all(patients$age_factor[patients$age > 60] == 1.2))
  expect_true(all(patients$HLA_factor[patients$HLA_DRB1_03 == 0] == 1.0))
  expect_true(all(patients$HLA_factor[patients$HLA_DRB1_03 == 1] == 2.2))
})

test_that("VirtualCohort simulation works correctly", {
  # Create small cohort for testing
  cohort <- VirtualCohort$new(n_patients = 10, random_state = 123)
  
  # Test simulation (non-parallel for testing)
  results <- cohort$simulate_all(
    t_span = c(0, 84),
    save_timeseries = FALSE,
    parallel = FALSE
  )
  
  expect_true(is.data.frame(results))
  expect_true(nrow(results) <= 10)  # Some simulations might fail
  
  # Check that results contain patient characteristics
  patient_cols <- c('patient_id', 'age', 'sex', 'drug_type')
  expect_true(all(patient_cols %in% names(results)))
  
  # Check that results contain risk metrics
  risk_cols <- c('grade2_hypothyroidism', 'grade3_hypothyroidism')
  expect_true(all(risk_cols %in% names(results)))
  
  # Check value ranges
  if (nrow(results) > 0) {
    expect_true(all(results$grade2_hypothyroidism >= 0 & results$grade2_hypothyroidism <= 1))
    expect_true(all(results$grade3_hypothyroidism >= 0 & results$grade3_hypothyroidism <= 1))
  }
})

test_that("RiskStratifier class works correctly", {
  # Create dummy results data
  n_patients <- 100
  results <- data.frame(
    patient_id = sprintf("P%03d", 1:n_patients),
    age = rnorm(n_patients, 65, 10),
    sex_factor = sample(c(1.0, 1.3), n_patients, replace = TRUE),
    HLA_factor = sample(c(1.0, 2.2), n_patients, replace = TRUE, prob = c(0.85, 0.15)),
    TPO_Ab_log_titer = rnorm(n_patients, 0.5, 0.8),
    drug_type = sample(c('nivolumab', 'pembrolizumab'), n_patients, replace = TRUE),
    grade2_hypothyroidism = runif(n_patients, 0, 0.4),
    grade3_hypothyroidism = runif(n_patients, 0, 0.2),
    time_to_onset_days = sample(c(NA, runif(20, 30, 150)), n_patients, replace = TRUE),
    stringsAsFactors = FALSE
  )
  
  # Initialize risk stratifier
  stratifier <- RiskStratifier$new(results)
  
  expect_s3_class(stratifier, "RiskStratifier")
  expect_s3_class(stratifier, "R6")
  expect_identical(stratifier$results, results)
})

test_that("Risk stratification works correctly", {
  # Create dummy results data
  n_patients <- 100
  results <- data.frame(
    patient_id = sprintf("P%03d", 1:n_patients),
    grade2_hypothyroidism = runif(n_patients, 0, 0.5),
    stringsAsFactors = FALSE
  )
  
  stratifier <- RiskStratifier$new(results)
  
  # Test stratification
  stratification_results <- stratifier$stratify_patients()
  
  expect_true(is.list(stratification_results))
  expect_true('stratified_patients' %in% names(stratification_results))
  expect_true('group_statistics' %in% names(stratification_results))
  
  stratified_patients <- stratification_results$stratified_patients
  
  expect_true(is.data.frame(stratified_patients))
  expect_true('risk_group' %in% names(stratified_patients))
  expect_true('risk_score' %in% names(stratified_patients))
  expect_equal(nrow(stratified_patients), n_patients)
  
  # Check risk groups
  risk_groups <- levels(stratified_patients$risk_group)
  expect_true(all(risk_groups %in% c('Low', 'Moderate', 'High', 'Very High')))
  
  # Check that risk scores match the input
  expect_equal(stratified_patients$risk_score, results$grade2_hypothyroidism)
})

test_that("Risk factor analysis works correctly", {
  # Create dummy results data with predictors
  n_patients <- 200
  results <- data.frame(
    patient_id = sprintf("P%03d", 1:n_patients),
    age = rnorm(n_patients, 65, 10),
    sex_factor = sample(c(1.0, 1.3), n_patients, replace = TRUE),
    HLA_factor = sample(c(1.0, 2.2), n_patients, replace = TRUE, prob = c(0.85, 0.15)),
    TPO_Ab_log_titer = rnorm(n_patients, 0.5, 0.8),
    grade2_hypothyroidism = rbinom(n_patients, 1, 0.2),  # Binary outcome
    stringsAsFactors = FALSE
  )
  
  stratifier <- RiskStratifier$new(results)
  
  # Test risk factor analysis
  risk_analysis <- stratifier$analyze_risk_factors()
  
  expect_true(is.list(risk_analysis))
  expect_true('model' %in% names(risk_analysis))
  expect_true('analysis' %in% names(risk_analysis))
  expect_true('model_summary' %in% names(risk_analysis))
  
  # Check model
  expect_s3_class(risk_analysis$model, "glm")
  
  # Check analysis results
  analysis_df <- risk_analysis$analysis
  expect_true(is.data.frame(analysis_df))
  
  required_cols <- c('predictor', 'coefficient', 'std_error', 'p_value', 
                    'odds_ratio', 'ci_lower', 'ci_upper')
  expect_true(all(required_cols %in% names(analysis_df)))
  
  # Check that odds ratios are positive
  expect_true(all(analysis_df$odds_ratio > 0))
  
  # Check that confidence intervals are reasonable
  expect_true(all(analysis_df$ci_lower <= analysis_df$odds_ratio))
  expect_true(all(analysis_df$odds_ratio <= analysis_df$ci_upper))
})

test_that("Biomarker analysis works correctly", {
  # Create dummy results data
  n_patients <- 100
  results <- data.frame(
    patient_id = sprintf("P%03d", 1:n_patients),
    biomarker_score = rnorm(n_patients, 50, 15),
    grade2_hypothyroidism = rbinom(n_patients, 1, 0.2),
    stringsAsFactors = FALSE
  )
  
  stratifier <- RiskStratifier$new(results)
  
  # Test biomarker analysis
  biomarker_results <- stratifier$biomarker_analysis('biomarker_score')
  
  expect_true(is.list(biomarker_results))
  expect_true('performance_metrics' %in% names(biomarker_results))
  expect_true('auc' %in% names(biomarker_results))
  expect_true('biomarker_summary' %in% names(biomarker_results))
  expect_true('outcome_prevalence' %in% names(biomarker_results))
  
  # Check performance metrics
  perf_metrics <- biomarker_results$performance_metrics
  expect_true(is.data.frame(perf_metrics))
  
  required_cols <- c('threshold_percentile', 'threshold_value', 'sensitivity', 
                    'specificity', 'ppv', 'npv')
  expect_true(all(required_cols %in% names(perf_metrics)))
  
  # Check that performance metrics are in valid ranges
  expect_true(all(perf_metrics$sensitivity >= 0 & perf_metrics$sensitivity <= 1))
  expect_true(all(perf_metrics$specificity >= 0 & perf_metrics$specificity <= 1))
  expect_true(all(perf_metrics$ppv >= 0 & perf_metrics$ppv <= 1, na.rm = TRUE))
  expect_true(all(perf_metrics$npv >= 0 & perf_metrics$npv <= 1, na.rm = TRUE))
  
  # Check outcome prevalence
  expect_true(biomarker_results$outcome_prevalence >= 0)
  expect_true(biomarker_results$outcome_prevalence <= 1)
  expect_equal(biomarker_results$outcome_prevalence, mean(results$grade2_hypothyroidism))
})

test_that("Population analysis handles edge cases correctly", {
  # Test with very small cohort
  small_cohort <- VirtualCohort$new(n_patients = 2, random_state = 123)
  expect_equal(nrow(small_cohort$patients), 2)
  
  # Test with custom population parameters
  custom_params <- PopulationParameters$new(
    age_mean = 50.0,
    female_proportion = 0.8,
    HLA_DRB1_03_prevalence = 0.3
  )
  
  custom_cohort <- VirtualCohort$new(n_patients = 50, pop_params = custom_params, random_state = 123)
  
  # Check that custom parameters are reflected
  patients <- custom_cohort$patients
  expect_true(abs(mean(patients$age) - 50.0) < 5.0)  # Should be close to 50
  expect_true(abs(mean(patients$sex == 'F') - 0.8) < 0.15)  # Should be close to 80% female
  expect_true(abs(mean(patients$HLA_DRB1_03 == 1) - 0.3) < 0.15)  # Should be close to 30% HLA+
  
  # Test risk stratifier with missing data
  results_with_na <- data.frame(
    patient_id = c("P001", "P002", "P003"),
    grade2_hypothyroidism = c(0.1, NA, 0.3),
    biomarker = c(10, 20, NA),
    stringsAsFactors = FALSE
  )
  
  stratifier_na <- RiskStratifier$new(results_with_na)
  
  # Should handle missing values gracefully
  expect_error(stratifier_na$biomarker_analysis('nonexistent_biomarker'))
})
