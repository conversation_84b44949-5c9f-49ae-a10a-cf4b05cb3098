#!/usr/bin/env Rscript
#' QSP Thyroid Model - Quick Test
#' ==============================
#'
#' This script performs a minimal test of the QSP thyroid model
#' to verify basic functionality without running the full example.
#'
#' Use this to quickly check if the model is working before
#' running the full example_usage.R script.
#'
#' <AUTHOR> Modeling Team
#' @date 2024

cat("================================================================================\n")
cat("QSP THYROID MODEL - QUICK TEST\n")
cat("================================================================================\n")

# Force output to console
options(width = 80)
flush.console()

# =============================================================================
# STEP 1: LOAD REQUIRED PACKAGES
# =============================================================================

cat("STEP 1: Loading required packages...\n")

required_packages <- c("R6", "deSolve", "dplyr", "futile.logger", "pracma")
missing_packages <- c()

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    missing_packages <- c(missing_packages, pkg)
  }
}

if (length(missing_packages) > 0) {
  cat("ERROR: Missing required packages:", paste(missing_packages, collapse = ", "), "\n")
  cat("Please install with: install.packages(c(", 
      paste0("'", missing_packages, "'", collapse = ", "), "))\n")
  stop("Cannot proceed without required packages")
}

cat("✓ All required packages available\n\n")

# =============================================================================
# STEP 2: LOAD QSP MODEL
# =============================================================================

cat("STEP 2: Loading QSP model components...\n")

# Load core model
tryCatch({
  source("R/qsp_model_core.R")
  cat("✓ Core model loaded\n")
}, error = function(e) {
  cat("ERROR loading core model:", e$message, "\n")
  stop("Cannot proceed without core model")
})

# Load population analysis (needed for VirtualCohort)
tryCatch({
  source("R/qsp_population_analysis.R")
  cat("✓ Population analysis loaded\n")
}, error = function(e) {
  cat("ERROR loading population analysis:", e$message, "\n")
  stop("Cannot proceed without population analysis")
})

cat("\n")

# =============================================================================
# STEP 3: TEST SINGLE PATIENT SIMULATION
# =============================================================================

cat("STEP 3: Testing single patient simulation...\n")

# Test each drug type
drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
results_summary <- list()

for (drug in drugs) {
  cat("Testing", drug, "...\n")
  
  tryCatch({
    # Create model
    model <- QSPModel$new()
    
    # Run short simulation (1 week)
    result <- simulate_patient(
      model = model,
      t_span = c(0, 7),  # 1 week
      drug_type = drug
    )
    
    # Calculate basic metrics
    final_TSH <- tail(result$TSH, 1)
    final_T3 <- tail(result$T3, 1)
    max_drug_conc <- max(result$drug_concentration)
    
    # Store results
    results_summary[[drug]] <- list(
      success = TRUE,
      final_TSH = final_TSH,
      final_T3 = final_T3,
      max_drug_conc = max_drug_conc,
      immune_susceptible = model$params$immune_susceptible,
      susceptibility_level = model$params$susceptibility_level
    )
    
    cat("  ✓ Success - TSH:", round(final_TSH, 2), "mIU/L, T3:", round(final_T3, 2), "pmol/L\n")
    cat("    Immune susceptible:", model$params$immune_susceptible, 
        "(", model$params$susceptibility_level, ")\n")
    
  }, error = function(e) {
    cat("  ✗ Failed:", e$message, "\n")
    results_summary[[drug]] <- list(success = FALSE, error = e$message)
  })
}

cat("\n")

# =============================================================================
# STEP 4: TEST SMALL POPULATION SIMULATION
# =============================================================================

cat("STEP 4: Testing small population simulation (n=10)...\n")

tryCatch({
  # Create small cohort
  cohort <- VirtualCohort$new(n_patients = 10, random_state = 42)
  
  cat("✓ Virtual cohort created\n")
  cat("  Patients:", nrow(cohort$patients), "\n")
  cat("  Age range:", round(min(cohort$patients$age)), "-", round(max(cohort$patients$age)), "years\n")
  cat("  Female:", sum(cohort$patients$sex == 'F'), "/", nrow(cohort$patients), "\n")
  
  # Run simulation
  cat("Running population simulation...\n")
  pop_results <- cohort$simulate_all(
    t_span = c(0, 14),  # 2 weeks
    save_timeseries = FALSE,
    parallel = FALSE
  )
  
  # Calculate incidence rates
  any_hypo_rate <- mean(pop_results$any_hypothyroidism) * 100
  grade2_hypo_rate <- mean(pop_results$grade2_hypothyroidism) * 100
  
  cat("✓ Population simulation completed\n")
  cat("  Any hypothyroidism:", round(any_hypo_rate, 1), "%\n")
  cat("  Grade 2+ hypothyroidism:", round(grade2_hypo_rate, 1), "%\n")
  
  # Drug-specific rates
  if (requireNamespace("dplyr", quietly = TRUE)) {
    drug_rates <- pop_results %>%
      dplyr::group_by(drug_type) %>%
      dplyr::summarise(
        n = dplyr::n(),
        any_rate = mean(any_hypothyroidism) * 100,
        grade2_rate = mean(grade2_hypothyroidism) * 100,
        .groups = 'drop'
      )
    
    cat("  Drug-specific rates:\n")
    for (i in 1:nrow(drug_rates)) {
      row <- drug_rates[i, ]
      cat("    ", tools::toTitleCase(row$drug_type), "(n=", row$n, "):", 
          round(row$any_rate, 1), "% any,", 
          round(row$grade2_rate, 1), "% grade 2+\n")
    }
  }
  
  population_test_passed <- TRUE
  
}, error = function(e) {
  cat("✗ Population simulation failed:", e$message, "\n")
  population_test_passed <- FALSE
})

cat("\n")

# =============================================================================
# STEP 5: SUMMARY
# =============================================================================

cat("================================================================================\n")
cat("QUICK TEST SUMMARY\n")
cat("================================================================================\n")

# Count successful single patient tests
successful_drugs <- sum(sapply(results_summary, function(x) x$success))
total_drugs <- length(drugs)

cat("Single Patient Tests:", successful_drugs, "/", total_drugs, "passed\n")

if (successful_drugs == total_drugs) {
  cat("✓ All single patient simulations working\n")
} else {
  cat("⚠ Some single patient simulations failed\n")
  failed_drugs <- names(results_summary)[!sapply(results_summary, function(x) x$success)]
  cat("  Failed drugs:", paste(failed_drugs, collapse = ", "), "\n")
}

if (exists("population_test_passed") && population_test_passed) {
  cat("✓ Population simulation working\n")
} else {
  cat("⚠ Population simulation failed or not tested\n")
}

# Overall assessment
if (successful_drugs == total_drugs && exists("population_test_passed") && population_test_passed) {
  cat("\n🎉 OVERALL: QUICK TEST PASSED!\n")
  cat("The QSP thyroid model is working correctly.\n\n")
  cat("NEXT STEPS:\n")
  cat("1. Run the full example: source('R/example_usage.R')\n")
  cat("2. Or from command line: Rscript R/example_usage.R\n")
  cat("3. Check outputs in 'example_outputs_R' directory\n")
  
  # Show expected vs actual incidence rates
  if (exists("any_hypo_rate")) {
    cat("\nNOTE: Incidence rates from small test (n=10):\n")
    cat("- Any hypothyroidism:", round(any_hypo_rate, 1), "% (target: 10-20%)\n")
    cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate, 1), "% (target: 5-15%)\n")
    cat("- Run larger population (n=100+) for more stable estimates\n")
  }
  
} else {
  cat("\n⚠ OVERALL: ISSUES DETECTED\n")
  cat("Some components are not working correctly.\n\n")
  cat("RECOMMENDED ACTIONS:\n")
  cat("1. Run diagnostic check: source('R/diagnostic_check.R')\n")
  cat("2. Check error messages above\n")
  cat("3. Verify all required packages are installed\n")
  cat("4. Check file paths and permissions\n")
}

cat("\n")
cat("Quick test completed at:", format(Sys.time()), "\n")
cat("================================================================================\n")
