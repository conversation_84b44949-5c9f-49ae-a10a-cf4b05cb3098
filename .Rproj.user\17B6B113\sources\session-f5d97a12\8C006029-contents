# Step-by-Step Execution Guide: QSP Thyroid Model

## Quick Start (Recommended Approach)

### Step 1: Run Diagnostic Check First
```r
# In R console or RStudio:
source("R/diagnostic_check.R")

# Or from command line:
Rscript R/diagnostic_check.R
```

**Expected Output**: Comprehensive system check with ✓/✗ status indicators

### Step 2: Install Missing Packages (if needed)
```r
# Core packages (REQUIRED):
install.packages(c("R6", "deSolve", "dplyr", "futile.logger", "pracma"))

# Additional packages (for full functionality):
install.packages(c("ggplot2", "readr", "purrr", "scales", "gridExtra", "viridis"))
```

### Step 3: Run Quick Test
```r
# Test basic functionality:
source("R/quick_test.R")

# Or from command line:
Rscript R/quick_test.R
```

**Expected Output**: Quick validation with single patient and small population tests

### Step 4: Run Main Example
Choose one of these options:

#### Option A: Minimal Example (No Plotting)
```r
source("R/minimal_example.R")
# Or: Rscript R/minimal_example.R
```

#### Option B: Full Example (With Plotting)
```r
source("R/example_usage.R")
# Or: Rscript R/example_usage.R
```

## Detailed Troubleshooting

### Problem 1: "No output or response"

**Possible Causes:**
1. Script running in non-interactive mode
2. Missing packages causing silent failures
3. Working directory issues
4. R not properly installed

**Solutions:**
```r
# Force interactive mode:
options(interactive = TRUE)
source("R/example_usage.R")

# Check working directory:
getwd()  # Should show path to QSP_PD_thyroid

# Set correct directory if needed:
setwd("path/to/QSP_PD_thyroid")
```

### Problem 2: Package Installation Issues

**For Windows:**
```r
# Use specific CRAN mirror:
install.packages("R6", repos = "https://cran.r-project.org")

# If behind firewall:
options(download.file.method = "wininet")
install.packages("R6")
```

**For Corporate Networks:**
```r
# Set proxy if needed:
Sys.setenv(http_proxy = "http://proxy.company.com:8080")
Sys.setenv(https_proxy = "https://proxy.company.com:8080")
```

### Problem 3: File Not Found Errors

**Check File Structure:**
```
QSP_PD_thyroid/
├── R/
│   ├── qsp_model_core.R
│   ├── qsp_population_analysis.R
│   ├── example_usage.R
│   ├── plotting_functions.R
│   ├── diagnostic_check.R
│   ├── quick_test.R
│   └── minimal_example.R
└── [other files]
```

**Verify Files Exist:**
```r
file.exists("R/qsp_model_core.R")  # Should return TRUE
```

### Problem 4: ODE Solver Issues

**Common Error:** "deSolve package not found"
```r
# Install deSolve:
install.packages("deSolve")

# Test installation:
library(deSolve)
```

**Common Error:** "Integration failed"
- Usually indicates parameter issues
- Run diagnostic to check model parameters

## Command Line Execution

### Windows Command Prompt:
```cmd
cd "C:\path\to\QSP_PD_thyroid"
Rscript R/diagnostic_check.R
Rscript R/minimal_example.R
```

### Windows PowerShell:
```powershell
cd "C:\path\to\QSP_PD_thyroid"
Rscript.exe R/diagnostic_check.R
Rscript.exe R/minimal_example.R
```

### If Rscript not in PATH:
```cmd
"C:\Program Files\R\R-4.x.x\bin\Rscript.exe" R/minimal_example.R
```

## RStudio Execution

1. **Open RStudio**
2. **Set Working Directory:**
   - Session → Set Working Directory → Choose Directory
   - Navigate to QSP_PD_thyroid folder
3. **Run Scripts:**
   ```r
   source("R/diagnostic_check.R")
   source("R/minimal_example.R")
   ```

## Expected Outputs

### Diagnostic Check Success:
```
✓ OVERALL STATUS: READY TO RUN
  All diagnostics passed. The QSP model should run successfully.
```

### Quick Test Success:
```
🎉 OVERALL: QUICK TEST PASSED!
The QSP thyroid model is working correctly.
```

### Minimal Example Success:
```
================================================================================
MINIMAL EXAMPLE COMPLETED SUCCESSFULLY!
================================================================================
All outputs saved to: minimal_outputs_R
- Population analysis: 100 patients
- Incidence rates: 12.0% any, 8.0% grade 2+
- Model validation: PASSED
✓ Recalibration successful - realistic incidence rates achieved!
```

## Output Files

After successful execution, you should see:

### Directory Structure:
```
QSP_PD_thyroid/
├── minimal_outputs_R/          # From minimal_example.R
│   ├── population_results.csv
│   ├── analysis_summary.txt
│   └── single_patient_*_timeseries.csv
├── example_outputs_R/          # From example_usage.R
│   ├── population_results.csv
│   ├── analysis_summary.txt
│   ├── single_patient_*/
│   └── population_plots/
└── virtual_cohort_results.csv  # From population simulation
```

### Key Result Files:
- **population_results.csv**: Individual patient outcomes
- **analysis_summary.txt**: Summary statistics and validation
- **single_patient_*_timeseries.csv**: Time-course data for each drug

## Validation Criteria

### Success Indicators:
- ✅ Overall any hypothyroidism: 10-20%
- ✅ Overall grade 2+ hypothyroidism: 5-15%
- ✅ Drug ranking preserved: Nivolumab > Pembrolizumab > Atezolizumab > Durvalumab
- ✅ No error messages during execution
- ✅ Output files created successfully

### Target Ranges by Drug:
| Drug | Any Hypothyroidism | Grade 2+ Hypothyroidism |
|------|-------------------|------------------------|
| Nivolumab | 10-20% | 5-15% |
| Pembrolizumab | 8-16% | 4-12% |
| Atezolizumab | 5-12% | 2-8% |
| Durvalumab | 3-10% | 1-6% |

## Getting Help

### If Problems Persist:

1. **Run full diagnostic:**
   ```r
   source("R/diagnostic_check.R")
   ```

2. **Check R version:**
   ```r
   R.version.string  # Should be 4.0.0 or higher
   ```

3. **Verify package versions:**
   ```r
   packageVersion("R6")      # Should be recent
   packageVersion("deSolve")  # Should be recent
   ```

4. **Check for error messages:**
   - Look for red text in R console
   - Check if files are being created
   - Verify working directory is correct

5. **Try minimal approach:**
   - Start with `diagnostic_check.R`
   - Then `quick_test.R`
   - Finally `minimal_example.R`
   - Only try `example_usage.R` after others work

### Common Solutions:
- **Restart R session** if packages seem corrupted
- **Update R** if version is very old (< 4.0)
- **Install packages one by one** to identify problematic ones
- **Check internet connection** for package downloads
- **Run as administrator** if permission issues on Windows

This guide should help you successfully run the recalibrated QSP thyroid model and verify that it produces realistic hypothyroidism incidence rates!
